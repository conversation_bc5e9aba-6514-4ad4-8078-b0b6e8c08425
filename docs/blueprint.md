# **App Name**: Multilingual Data Entry

## Core Features:

- Multilingual Support: Display all static text in Chinese, English, or Korean, based on the user's system language settings, with English as the default.
- Image and Data Input: Enable users to upload 1-9 images, defaulting to 3. For each image, display fields for 'Photo Number' and 'Device Status,' both as editable dropdowns/input fields. The dropdown contents are populated via a configurable function. After uploading, the application will consume and display the json that is returned from `http://{API_BASE_URL}/process-image`
- Data Submission & Excel Export: Allow users to modify the displayed information, then submit the images and all the displayed information to the backend endpoint `http://{API_BASE_URL}/generate-excel`. The API will return an Excel file.

## Style Guidelines:

- Primary color: Neutral white or light gray for the background to ensure readability.
- Secondary color: Use a muted blue or green for primary interactive elements.
- Accent: Teal (#008080) for highlights, progress indicators, and confirmation messages.
- Use a grid-based layout for the images and data fields to ensure a structured and responsive design.
- Use clear and recognizable icons for actions like 'Upload', 'Download', and 'Submit'.
- Use subtle transitions and animations for loading states and form submissions to improve user experience.