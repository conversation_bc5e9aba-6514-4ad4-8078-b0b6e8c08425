
"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput, // Import CommandInput
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useTranslations } from "@/hooks/use-translations"

interface ComboboxInputProps {
  options: string[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  inputPlaceholder?: string; // Renamed for clarity, used in CommandInput
  emptyText?: string;
  className?: string;
  disabled?: boolean; // Added disabled prop
  'aria-label'?: string; // Explicitly allow aria-label
}

export function ComboboxInput({
  options,
  value,
  onChange,
  placeholder,
  inputPlaceholder, // Renamed prop
  emptyText,
  className,
  disabled = false, // Default disabled to false
  'aria-label': ariaLabel, // Destructure aria-label
}: ComboboxInputProps) {
  const { t } = useTranslations();
  const [open, setOpen] = React.useState(false);
  // No separate inputValue needed, CommandInput handles it internally

  const handleSelect = (currentValue: string) => {
    // Check if the selected value is already the current value
    // This prevents closing the popover if the user re-selects the same item
    // It allows selecting the displayed value to confirm it if needed.
    // const newValue = currentValue === value ? "" : currentValue; // Old logic might clear selection on re-click
    onChange(currentValue); // Directly set the selected value
    setOpen(false); // Close popover on selection
  };

  // Handle input changes directly within CommandInput
  const handleInputChange = (search: string) => {
     onChange(search); // Update parent state immediately as user types
  };


  const displayValue = value
    ? options.find((option) => option.toLowerCase() === value.toLowerCase()) || value // Show typed value if not in options
    : (placeholder || t('selectOrEnter')); // Changed default placeholder

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)} // Use full width by default
          disabled={disabled} // Apply disabled state
          aria-label={ariaLabel || placeholder || t('selectOrEnter')} // Add aria-label, changed default
        >
          <span className="truncate">{displayValue}</span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0"> {/* Match trigger width */}
        <Command shouldFilter={true}> {/* Let Command handle filtering */}
           <CommandInput
             placeholder={inputPlaceholder || t('searchOrEnter')} // Use translation
             value={value} // Control the CommandInput value
             onValueChange={handleInputChange} // Use onValueChange for CommandInput
             disabled={disabled} // Apply disabled state
             aria-label={ariaLabel || t('comboboxInputLabel')} // Use translation or passed label
           />
          <CommandList>
            <CommandEmpty>{emptyText || t('noResultsFound')}</CommandEmpty> {/* Updated empty text */}
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option}
                  value={option} // Value used for filtering and selection
                  onSelect={handleSelect}
                  disabled={disabled} // Disable items if needed
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value.toLowerCase() === option.toLowerCase() ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {option}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
    // Removed the separate Input field
  )
}
