
'use client';

import React from 'react';
import Image from 'next/image';
import { Upload, X, Loader2, ImageOff, Camera, Images, Expand, RotateCw } from 'lucide-react';
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ComboboxInput } from "@/components/combobox-input";
import type { SingleImageData, DropdownOptions } from '@/types';
import { useTranslations } from '@/hooks/use-translations';
import { cn } from '@/lib/utils';
import { useFitImage } from '@/hooks/use-fit-image';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';


interface ImageUploadCardProps {
  imageData: SingleImageData;
  options: Pick<DropdownOptions, 'deviceStatus'>;
  onImageChange: (id: string, file: File | null) => void;
  onDataChange: (id: string, field: keyof Pick<SingleImageData, 'deviceStatus' | 'angle'>, value: string | number) => void;
  onRemove: (id: string) => void;
  uploadMode?: 'album' | 'camera';
  className?: string;
}

export function ImageUploadCard({
  imageData,
  options,
  onImageChange,
  onDataChange,
  onRemove,
  uploadMode = 'album',
  className
}: ImageUploadCardProps) {
  const { t } = useTranslations();
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [uniqueId, setUniqueId] = React.useState('');
  const containerRef = React.useRef<HTMLDivElement>(null);
  const { transform } = useFitImage(containerRef, imageData.angle || 0);
  const [isFullScreen, setIsFullScreen] = React.useState(false);


  React.useEffect(() => {
     const generateId = () => typeof crypto !== 'undefined' && crypto.randomUUID ? crypto.randomUUID() : `file-upload-${imageData.id}-${Math.random().toString(36).substring(7)}`;
     setUniqueId(generateId());
  }, [imageData.id]);


  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] ?? null;
    onImageChange(imageData.id, file);
     if (event.target) {
        event.target.value = '';
     }
  };

  const handleRemoveClick = () => {
    onRemove(imageData.id);
     if (inputRef.current) {
        inputRef.current.value = '';
     }
  };

  const handleRetryUpload = () => {
     if (imageData.file) {
        onDataChange(imageData.id, 'deviceStatus', '');
        onImageChange(imageData.id, imageData.file);
     }
  };
  
  const handleRotateClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    onDataChange(imageData.id, 'angle', (imageData.angle || 0) + 90);
  };


  const fileInputId = uniqueId || undefined;
  const hasImage = !!imageData.file;
  const isProcessing = imageData.isProcessing;
  

  return (
    <>
    <Card className={cn(
        "w-full h-full flex flex-col overflow-hidden shadow-md transition-all duration-300 ease-in-out hover:shadow-lg relative",
         className
    )}>
      <CardHeader className="relative p-4 border-b">
         <Button
             type="button"
             variant="destructive"
             size="icon"
             className="absolute top-2 left-2 z-10 h-7 w-7 opacity-80 hover:opacity-100"
             onClick={handleRemoveClick}
             disabled={isProcessing}
             aria-label={hasImage ? t('removeImage') : t('removeSlot')}
           >
             <X className="h-4 w-4" />
           </Button>

        <div ref={containerRef} className="aspect-video relative bg-muted rounded-md overflow-hidden flex items-center justify-center">
          {imageData.previewUrl ? (
            <>
              <div className="absolute top-2 right-2 z-10 flex flex-col space-y-1">
                <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 text-white bg-black/50 hover:bg-black/75"
                    onClick={(e) => { e.stopPropagation(); setIsFullScreen(true); }}
                >
                    <Expand className="h-4 w-4" />
                </Button>
                <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 text-white bg-black/50 hover:bg-black/75"
                    onClick={handleRotateClick}
                >
                    <RotateCw className="h-4 w-4" />
                </Button>
              </div>
              <label htmlFor={fileInputId} className="w-full h-full flex items-center justify-center cursor-pointer">
                <div style={{transform, transition: 'transform 0.3s ease', width: '100%', height: '100%', position: 'relative'}}>
                  <Image
                    src={imageData.previewUrl}
                    alt={t('imagePreviewAlt')}
                    fill
                    style={{objectFit: 'contain'}}
                    data-ai-hint="equipment machinery industrial"
                  />
                </div>
              </label>
            </>
          ) : (
             fileInputId ? (
                 <label htmlFor={fileInputId} className="cursor-pointer text-muted-foreground transition-colors hover:text-primary flex flex-col items-center gap-2">
                    {uploadMode === 'camera' ? <Camera className="h-10 w-10" /> : <Upload className="h-10 w-10" />}
                    <span>{t('uploadImages')}</span>
                 </label>
             ) : (
                 <div className="text-muted-foreground flex flex-col items-center gap-2">
                      <Loader2 className="h-10 w-10 animate-spin" />
                      <span>{t('loading')}</span>
                  </div>
             )
          )}
           {fileInputId && (
                <input
                    id={fileInputId}
                    ref={inputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="sr-only"
                    {...(uploadMode === 'camera' && { capture: 'environment' })}
                />
            )}
        </div>
      </CardHeader>
      <CardContent className="p-4 space-y-4 flex-grow">
        {imageData.isProcessing && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>{t('uploading')}</span>
            </div>
        )}
        {imageData.error && !isProcessing && (
          <Alert variant="destructive">
             <AlertDescription className="flex items-center justify-between">
              <span>{t('uploadError')} {imageData.error}</span>
              {imageData.file && (
                <Button variant="ghost" size="sm" onClick={handleRetryUpload} className="ml-2" disabled={isProcessing}>
                   {t('retry')}
                </Button>
              )}
            </AlertDescription>
          </Alert>
        )}

        { imageData.previewUrl && (
            <>
                <div className="space-y-1">
                    <ComboboxInput
                        options={options.deviceStatus}
                        value={imageData.deviceStatus}
                        onChange={(value) => onDataChange(imageData.id, 'deviceStatus', value)}
                        placeholder={t('selectOption')}
                        inputPlaceholder={t('searchOrEnter')}
                        emptyText={t('noStatusFound')}
                        className="w-full"
                        disabled={isProcessing || imageData.isStatusFixed}
                        aria-label={t('deviceStatusInputLabel')}
                    />
                </div>
            </>
         )}
      </CardContent>
    </Card>
    <Dialog open={isFullScreen} onOpenChange={setIsFullScreen}>
      <DialogContent className="p-0 m-0 w-screen h-screen max-w-none border-none bg-black/80">
        <DialogHeader className="sr-only">
          <DialogTitle>{t('imagePreviewAlt')}</DialogTitle>
        </DialogHeader>
        {imageData.previewUrl && (
          <div className="w-full h-full flex items-center justify-center p-4" onClick={() => setIsFullScreen(false)}>
            <div style={{transform, transition: 'transform 0.3s ease', width: '100%', height: '100%', position: 'relative'}}>
              <Image
                src={imageData.previewUrl}
                alt="Full screen preview"
                fill
                style={{objectFit: 'contain'}}
              />
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
    </>
  );
}
