
'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
  useSidebar,
} from '@/components/ui/sidebar';
import { useTranslations } from '@/hooks/use-translations';
import { useAuth } from '@/contexts/auth-context';
import { getAdminInfo, removeAdminToken, removeAdminInfo } from '@/lib/admin-token';
import { getToken } from '@/lib/token';
import { Button } from './ui/button';
import { Home, QrCode, FileText, Settings, UserCog, LogOut, FileUp, ListChecks, Database } from 'lucide-react';

// Helper function to decode JWT token and extract username
const getUsernameFromToken = (token: string): string | null => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.username || payload.sub || null;
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

const userMenuItems = [
  { href: '/qr/register', labelKey: 'qrRegisterTitle', icon: QrCode },
  { href: '/qr/register-batch', labelKey: 'qrBatchRegisterTitle', icon: FileUp },
  { href: '/qr/record', labelKey: 'qrRecordTitle', icon: QrCode },
  { href: '/cv/record', labelKey: 'appName', icon: FileText },
  { href: '/cv/nameplate', labelKey: 'extractTableFromImage', icon: FileText },
];

const adminMenuItems = [
  { href: '/admin/dashboard', labelKey: 'adminAccountManagement', icon: UserCog },
  { href: '/admin/qr_record', labelKey: 'adminRecordManagement', icon: Database },
  { href: '/admin/record', labelKey: 'adminRecordManagementOld', icon: ListChecks },
];

export function AppSidebar() {
  const pathname = usePathname();
  const { t } = useTranslations();
  const { logout } = useAuth();
  const { setOpenMobile, isMobile } = useSidebar();
  const [isClient, setIsClient] = useState(false);
  const [currentUser, setCurrentUser] = useState<string | null>(null);
  const [isAdminPage, setIsAdminPage] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return; // Only run on client side

    // Check if current page is admin page
    const isAdmin = pathname.startsWith('/admin/');
    setIsAdminPage(isAdmin);

    // Get current user info based on page type
    if (isAdmin) {
      const adminInfo = getAdminInfo();
      if (adminInfo) {
        setCurrentUser(adminInfo.username);
      } else {
        setCurrentUser(null);
      }
    } else {
      // For regular users, get username from token
      const userToken = getToken();
      if (userToken) {
        const username = getUsernameFromToken(userToken);
        setCurrentUser(username);
      } else {
        setCurrentUser(null);
      }
    }
  }, [pathname, isClient]);

  const handleLinkClick = () => {
    if (isMobile) {
      setOpenMobile(false);
    }
  };

  const handleLogoutClick = () => {
    if (isMobile) {
      setOpenMobile(false);
    }

    if (isAdminPage) {
      // Admin logout
      removeAdminToken();
      removeAdminInfo();
      window.location.href = '/admin/login';
    } else {
      // Regular user logout
      logout();
    }
  };

  if (!isClient) {
    return null; // Don't render on the server to prevent hydration mismatch
  }

  return (
    <Sidebar>
      <SidebarHeader />
      <SidebarContent>
        <SidebarMenu>
          {userMenuItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              <Link href={item.href} legacyBehavior passHref>
                <SidebarMenuButton
                  isActive={pathname === item.href}
                  tooltip={t(item.labelKey as any)}
                  onClick={handleLinkClick}
                >
                  <item.icon />
                  <span>{t(item.labelKey as any)}</span>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          ))}

          {/* Separator for admin functions */}
          <div className="my-2 border-t border-sidebar-border" />

          {adminMenuItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              <Link href={item.href} legacyBehavior passHref>
                <SidebarMenuButton
                  isActive={pathname === item.href}
                  tooltip={t(item.labelKey as any)}
                  onClick={handleLinkClick}
                >
                  <item.icon />
                  <span>{t(item.labelKey as any)}</span>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
            <SidebarMenuItem>
                <SidebarMenuButton onClick={handleLogoutClick} tooltip={t('logoutButton')}>
                    <LogOut />
                    <div className="flex flex-col items-start">
                      <span className="text-sm">{t('logoutButton')}</span>
                      {isClient && currentUser && (
                        <span className="text-xs text-muted-foreground truncate max-w-[120px]">
                          {currentUser}
                        </span>
                      )}
                    </div>
                </SidebarMenuButton>
            </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
