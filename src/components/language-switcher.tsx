'use client';

import { useTranslations } from '@/hooks/use-translations';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Languages } from 'lucide-react';

export function LanguageSwitcher() {
  const { setLanguage, isEnabled, t } = useTranslations();

  if (!isEnabled) {
    return null;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Languages className="h-[1.2rem] w-[1.2rem]" />
          <span className="sr-only">{t('changeLanguage')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setLanguage('en')}>
          {t('languageNameEn')}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setLanguage('zh')}>
          {t('languageNameZh')}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setLanguage('ko')}>
          {t('languageNameKo')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
