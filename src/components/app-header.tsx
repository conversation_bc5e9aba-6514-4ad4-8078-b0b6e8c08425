'use client';

import {
  SidebarTrigger,
} from '@/components/ui/sidebar';
import { useTranslations } from '@/hooks/use-translations';
import {Languages} from 'lucide-react'
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type {Language} from '@/types'
import { LanguageSwitcher } from '@/components/language-switcher';

export function AppHeader({ title }: { title: string }) {
    const { t, language, setLanguage, isEnabled: isTranslationEnabled } = useTranslations();
    
    const handleLanguageChange = (lang: Language) => {
        setLanguage(lang);
    };

    return (
        <header className="sticky top-0 z-10 flex h-14 items-center gap-4 border-b bg-background px-4 sm:static sm:h-auto sm:border-0 sm:bg-transparent sm:px-6 py-2">
            <SidebarTrigger />
            <h1 className="text-xl font-semibold">{title}</h1>

            <div className="ml-auto flex items-center gap-2">
                <LanguageSwitcher />
            </div>
        </header>
    )
}
