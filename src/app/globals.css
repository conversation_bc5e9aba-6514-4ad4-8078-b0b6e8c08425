@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 220 10% 98%; /* Light Gray */
    --foreground: 240 6% 10%; /* Dark Gray */
    --card: 220 10% 98%;
    --card-foreground: 240 6% 10%;
    --popover: 220 10% 98%;
    --popover-foreground: 240 6% 10%;
    --primary: 210 40% 50%; /* Muted Blue */
    --primary-foreground: 0 0% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 210 40% 30%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 210 40% 45.1%;
    --accent: 180 100% 25.1%; /* Teal */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 40% 89.8%;
    --input: 210 40% 89.8%;
    --ring: 210 40% 50%; /* Muted Blue for ring */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 220 10% 98%;
    --sidebar-foreground: 240 6% 10%;
    --sidebar-primary: 210 40% 50%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 210 40% 30%;
    --sidebar-border: 210 40% 89.8%;
    --sidebar-ring: 210 40% 50%;
  }
  .dark {
    /* Define dark theme variables if needed, otherwise keep defaults */
    --background: 240 6% 10%;
    --foreground: 210 40% 98%;
    --card: 240 6% 10%;
    --card-foreground: 210 40% 98%;
    --popover: 240 6% 10%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 50%; /* Muted Blue */
    --primary-foreground: 0 0% 98%;
    --secondary: 210 40% 14.9%;
    --secondary-foreground: 210 40% 98%;
    --muted: 210 40% 14.9%;
    --muted-foreground: 210 40% 63.9%;
    --accent: 180 100% 25.1%; /* Teal */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 40% 14.9%;
    --input: 210 40% 14.9%;
    --ring: 210 40% 50%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 6% 10%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 210 40% 50%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 210 40% 14.9%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 210 40% 14.9%;
    --sidebar-ring: 210 40% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Add custom grid class */
@layer components {
  .grid-auto-fill-300 {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}
