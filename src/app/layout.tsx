
import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google'; 
import './globals.css';
import { TranslationProvider } from '@/hooks/use-translations';
import { AuthProvider } from '@/contexts/auth-context'; 
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/app-sidebar';
import { AppHeader } from '@/components/app-header';
import Script from 'next/script';

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' });

export const metadata: Metadata = {
  title: 'Data Entry', 
  description: 'Application for data entry and image processing.',
  manifest: '/manifest.json',
  themeColor: '#ffffff',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html>
      <body className={`${inter.variable} font-sans antialiased`}>
        <AuthProvider> 
          <TranslationProvider>
            <SidebarProvider>
              <AppSidebar />
              <main className="flex-1">
                {children}
              </main>
            </SidebarProvider>
          </TranslationProvider>
        </AuthProvider>
        <Script src="/libs/OpencvQr.js" strategy="beforeInteractive" />
      </body>
    </html>
  );
}
