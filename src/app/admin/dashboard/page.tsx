
'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { getAdminToken, removeAdminToken, getAdminInfo, removeAdminInfo } from '@/lib/admin-token';
import { useToast } from '@/hooks/use-toast';
import { useTranslations } from '@/hooks/use-translations';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { Loader2, LogOut, PlusCircle, MoreHorizontal, Languages, UserCog, Users, BarChart3, Edit3, KeyRound, UserCheck, UserX, ShieldAlert, Briefcase, CalendarClock, AlertCircle, ListChecks, RefreshCw, Settings, Mail, Send, Download, Search, QrCode } from 'lucide-react';
import type { EndUser, UserSummary, AdminUsersResponse, AddUserPayload, UpdateUserEmailPayload, UpdateUserPasswordPayload, UpdateUserStatusPayload, AdminApiResponse, Language, AdminInfo, TranslationKey, RecordItem, AdminRecordsResponse, EmailConfigGetResponse, EmailConfigUpdatePayload, EmailConfigTestResponse } from '@/types';
import { format, differenceInCalendarDays } from 'date-fns';
import { Toaster } from '@/components/ui/toaster';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AppHeader } from '@/components/app-header';


const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";

type UserDialogState = 
  | { type: 'addUser'; isOpen: boolean }
  | { type: 'editEmail'; isOpen: boolean; user: EndUser | null }
  | { type: 'editPassword'; isOpen: boolean; user: EndUser | null }
  | { type: 'confirmStatusChange'; isOpen: boolean; user: EndUser | null, newStatus: boolean };

const Highlight = ({ text, highlight }: { text: string; highlight: string }) => {
  if (!highlight.trim()) {
    return <span>{text}</span>;
  }
  const regex = new RegExp(`(${highlight})`, 'gi');
  const parts = text.split(regex);
  return (
    <span>
      {parts.map((part, i) =>
        regex.test(part) ? (
          <strong key={i} className="font-bold text-primary">{part}</strong>
        ) : (
          <span key={i}>{part}</span>
        )
      )}
    </span>
  );
};

export default function AdminDashboardPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { t, language, setLanguage, isEnabled: isTranslationEnabled } = useTranslations();

  const [adminToken, setAdminToken] = useState<string | null>(null);
  const [adminInfo, setAdminInfo] = useState<AdminInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // User management state
  const [users, setUsers] = useState<EndUser[]>([]);
  const [userSummary, setUserSummary] = useState<UserSummary | null>(null);
  const [isFetchingUsers, setIsFetchingUsers] = useState(false);
  const [showDisabledUsers, setShowDisabledUsers] = useState(true);
  

  

  const [userDialogState, setUserDialogState] = useState<UserDialogState>({ type: 'addUser', isOpen: false });

  const [addUserForm, setAddUserForm] = useState({ username: '', email: '', password: '', confirmPassword: '', passwordError: '', confirmPasswordError: '' });
  const [editEmailForm, setEditEmailForm] = useState({ email: '' });
  const [editPasswordForm, setEditPasswordForm] = useState({ password: '', confirmPassword: '', passwordError: '', confirmPasswordError: '' });
  const [isSubmittingUserDialog, setIsSubmittingUserDialog] = useState(false);

  // Email Configuration State
  const [currentEmailConfig, setCurrentEmailConfig] = useState<EmailConfigGetResponse | null>(null);
  const [isEmailConfigDialogOpen, setIsEmailConfigDialogOpen] = useState(false);
  const [emailConfigForm, setEmailConfigForm] = useState({
    senderEmail: '',
    senderPassword: '',
    smtpHost: '',
    smtpPort: '', // Store as string for input, convert to number on submit
    enableCustom: false,
  });
  const [isFetchingEmailConfig, setIsFetchingEmailConfig] = useState(false);
  const [isSavingEmailConfig, setIsSavingEmailConfig] = useState(false);
  const [isTestingEmailConfig, setIsTestingEmailConfig] = useState(false);


  const handleApiError = useCallback((error: any, defaultMessageKey: TranslationKey = 'adminGenericError') => {
    let message = t(defaultMessageKey);
    if (error instanceof Error) message = error.message;
    
    if (typeof error === 'object' && error !== null && error.error) {
        const errorMap: Record<string, TranslationKey> = {
            "Invalid credentials": 'adminInvalidCredentialsError',
            "Account has expired": 'adminAccountExpiredError',
            "Admin authentication required": 'adminAuthRequiredError',
            "User not found or not authorized": 'adminUserNotFoundError',
            "Email already exists": 'adminEmailExistsError',
            "Username already exists": 'adminUsernameExistsError',
            "Sub-account quota exceeded": 'adminQuotaExceededError',
            "Invalid days parameter. Must be a positive integer.": defaultMessageKey, 
            "Invalid sync_status parameter. Must be 'all', 'synced', or 'unsynced'.": defaultMessageKey,
            "Record not found": "adminRecordNotFound",
            // Email Config Errors
            "No email configuration found. Please configure your email settings first.": 'adminEmailConfigNoConfig',
            "Email configuration is disabled. Please enable your custom email configuration first.": 'adminEmailConfigDisabled',
            "Email configuration is incomplete. Please update your email settings.": 'adminEmailConfigIncomplete',
            "Failed to send test email": 'adminEmailConfigTestErrorGeneric',
        };
        message = t(errorMap[error.error] || defaultMessageKey);
        if (error.details && error.error === "Failed to send test email") {
          message = t('adminEmailConfigTestError', { details: error.details });
        }
    }
    
    toast({ title: t('errorTitle'), description: message, variant: 'destructive' });
  }, [toast, t]);

  const fetchUsers = useCallback(async (token: string | null) => {
    if (!token) return;
    setIsFetchingUsers(true);
    try {
      const response = await fetch(`${API_BASE_URL}/admin/users`, {
        headers: { 
            'Authorization': `Bearer ${token}`,
            'Accept-Language': language,
        },
      });
      const data: AdminUsersResponse = await response.json();
      if (!response.ok) {
        if (response.status === 401) {
          toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
          removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
        }
        throw data;
      }
      setUsers(data.users);
      setUserSummary(data.summary);
    } catch (error: any) {
      handleApiError(error, 'adminFetchingUsersError');
      setUsers([]); setUserSummary(null);
    } finally {
      setIsFetchingUsers(false);
    }
  }, [router, toast, t, handleApiError, language]);





  const fetchEmailConfig = useCallback(async (token: string | null) => {
    if (!token) return;
    setIsFetchingEmailConfig(true);
    try {
      const response = await fetch(`${API_BASE_URL}/admin/email-config`, {
        headers: { 
            'Authorization': `Bearer ${token}`,
            'Accept-Language': language,
        },
      });
      const data: EmailConfigGetResponse = await response.json();
      if (!response.ok) {
        if (response.status === 401) {
          toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
          removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
        }
        throw data;
      }
      setCurrentEmailConfig(data);
      setEmailConfigForm(prev => ({
        ...prev,
        senderEmail: data.sender_email || '',
        enableCustom: data.has_config && data.enabled,
        // Sensitive fields are cleared when dialog opens
        senderPassword: '',
        smtpHost: '',
        smtpPort: '',
      }));
    } catch (error: any) {
      handleApiError(error, 'adminEmailConfigFetchError');
      setCurrentEmailConfig(null);
    } finally {
      setIsFetchingEmailConfig(false);
    }
  }, [router, toast, t, handleApiError, language]);


  useEffect(() => {
    const token = getAdminToken();
    const info = getAdminInfo();
    if (!token) {
      router.push('/admin/login');
    } else {
      setAdminToken(token);
      if (info) setAdminInfo(info);
      fetchUsers(token);
      fetchEmailConfig(token);
      setIsLoading(false);

      const storedPreference = localStorage.getItem('admin_show_disabled_users');
      if (storedPreference !== null) {
        setShowDisabledUsers(storedPreference === 'true');
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router, fetchUsers, fetchEmailConfig]);

  const handleShowDisabledUsersChange = (checked: boolean) => {
    setShowDisabledUsers(checked);
    localStorage.setItem('admin_show_disabled_users', String(checked));
  };
  
  const handleLogout = () => {
    removeAdminToken();
    removeAdminInfo();
    router.push('/admin/login');
  };

  const openUserDialog = (type: UserDialogState['type'], user: EndUser | null = null, newStatus?: boolean) => {
    if (type === 'addUser') {
        setAddUserForm({ username: '', email: '', password: '', confirmPassword: '', passwordError: '', confirmPasswordError: '' });
        setUserDialogState({ type, isOpen: true });
    } else if (type === 'editEmail' && user) {
        setEditEmailForm({ email: user.email });
        setUserDialogState({ type, isOpen: true, user });
    } else if (type === 'editPassword' && user) {
        setEditPasswordForm({ password: '', confirmPassword: '', passwordError: '', confirmPasswordError: '' });
        setUserDialogState({ type, isOpen: true, user });
    } else if (type === 'confirmStatusChange' && user && typeof newStatus === 'boolean') {
        setUserDialogState({ type, isOpen: true, user, newStatus });
    }
  };

  const closeUserDialog = () => {
    if (userDialogState.type === 'addUser') {
      setAddUserForm({ username: '', email: '', password: '', confirmPassword: '', passwordError: '', confirmPasswordError: '' });
      setUserDialogState({ type: 'addUser', isOpen: false });
    }
    else if (userDialogState.type === 'editEmail') setUserDialogState({ type: 'editEmail', isOpen: false, user: null });
    else if (userDialogState.type === 'editPassword') {
      setEditPasswordForm({ password: '', confirmPassword: '', passwordError: '', confirmPasswordError: '' });
      setUserDialogState({ type: 'editPassword', isOpen: false, user: null });
    }
    else if (userDialogState.type === 'confirmStatusChange') setUserDialogState({ type: 'confirmStatusChange', isOpen: false, user: null, newStatus: false});
  };

  const handleAddUserSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Prevent submission if errors are displayed (validated onChange)
    if (addUserForm.passwordError || addUserForm.confirmPasswordError) {
      return;
    }

    if (!adminToken) return;
    setIsSubmittingUserDialog(true);
    const payload: AddUserPayload = { username: addUserForm.username, email: addUserForm.email, password: addUserForm.password };
    try {
      const response = await fetch(`${API_BASE_URL}/admin/users`, {
        method: 'POST',
        headers: { 
            'Authorization': `Bearer ${adminToken}`, 
            'Content-Type': 'application/json',
            'Accept-Language': language,
        },
        body: JSON.stringify(payload),
      });
      const data: AdminApiResponse = await response.json();
      if (!response.ok) {
        if (response.status === 401) {
          toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
          removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
        }
        throw data;
      }
      toast({ title: t('adminAddUserSuccess'), variant: 'default', className: "bg-accent text-accent-foreground" });
      fetchUsers(adminToken);
      closeUserDialog();
    } catch (error: any) {
      handleApiError(error, 'adminAddUserError');
    } finally {
      setIsSubmittingUserDialog(false);
    }
  };

  const handleEditEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!adminToken || userDialogState.type !== 'editEmail' || !userDialogState.user) return;
    const emailString = editEmailForm.email.trim();
    if (emailString !== "") {
        const emails = emailString.split(',').map(eml => eml.trim()).filter(eml => eml !== ""); 
        if (emails.length > 5) {
            toast({ title: t('errorTitle'), description: t('adminMaxEmailsError'), variant: 'destructive' });
            return;
        }
    }
    setIsSubmittingUserDialog(true);
    const payload: UpdateUserEmailPayload = { email: emailString }; 
    try {
      const response = await fetch(`${API_BASE_URL}/admin/users/${userDialogState.user.id}/email`, {
        method: 'PUT',
        headers: { 
            'Authorization': `Bearer ${adminToken}`, 
            'Content-Type': 'application/json',
            'Accept-Language': language,
        },
        body: JSON.stringify(payload),
      });
      const data: AdminApiResponse = await response.json();
      if (!response.ok) {
        if (response.status === 401) {
          toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
          removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
        }
        throw data;
      }
      toast({ title: t('adminUpdateEmailSuccess'), variant: 'default', className: "bg-accent text-accent-foreground" });
      fetchUsers(adminToken);
      closeUserDialog();
    } catch (error: any) {
      handleApiError(error, 'adminUpdateEmailError');
    } finally {
      setIsSubmittingUserDialog(false);
    }
  };

  const handleEditPasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Prevent submission if errors are displayed (validated onChange)
    if (editPasswordForm.passwordError || editPasswordForm.confirmPasswordError) {
        return;
    }

    if (!adminToken || userDialogState.type !== 'editPassword' || !userDialogState.user) return;
    setIsSubmittingUserDialog(true);
    const payload: UpdateUserPasswordPayload = { password: editPasswordForm.password };
    try {
      const response = await fetch(`${API_BASE_URL}/admin/users/${userDialogState.user.id}/password`, {
        method: 'PUT',
        headers: { 
            'Authorization': `Bearer ${adminToken}`, 
            'Content-Type': 'application/json',
            'Accept-Language': language,
        },
        body: JSON.stringify(payload),
      });
      const data: AdminApiResponse = await response.json();
      if (!response.ok) {
         if (response.status === 401) {
           toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
           removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
        }
        throw data;
      }
      toast({ title: t('adminUpdatePasswordSuccess'), variant: 'default', className: "bg-accent text-accent-foreground" });
      fetchUsers(adminToken);
      closeUserDialog();
    } catch (error: any) {
      handleApiError(error, 'adminUpdatePasswordError');
    } finally {
      setIsSubmittingUserDialog(false);
    }
  };

  const handleToggleUserStatus = async (user: EndUser, newStatus: boolean) => {
    if (!adminToken) return;
    openUserDialog('confirmStatusChange', user, newStatus);
  };

  const confirmUserStatusChange = async () => {
    if (!adminToken || userDialogState.type !== 'confirmStatusChange' || !userDialogState.user) return;
    setIsSubmittingUserDialog(true);
    const payload: UpdateUserStatusPayload = { is_disabled: userDialogState.newStatus };
    try {
      const response = await fetch(`${API_BASE_URL}/admin/users/${userDialogState.user.id}/status`, {
        method: 'PUT',
        headers: { 
            'Authorization': `Bearer ${adminToken}`, 
            'Content-Type': 'application/json',
            'Accept-Language': language,
        },
        body: JSON.stringify(payload),
      });
      const data: AdminApiResponse = await response.json();
      if (!response.ok) {
         if (response.status === 401) {
          toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
          removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
        }
        throw data;
      }
      toast({ title: t('adminUpdateStatusSuccess'), description: data.message, variant: 'default', className: "bg-accent text-accent-foreground" });
      fetchUsers(adminToken);
      closeUserDialog();
    } catch (error: any) {
      handleApiError(error, 'adminUpdateStatusError');
    } finally {
      setIsSubmittingUserDialog(false);
    }
  };

  const handleLanguageChange = (lang: Language) => {
    setLanguage(lang);
  };

  const displayUsers = useMemo(() => {
    if (showDisabledUsers) {
      return users;
    }
    return users.filter(user => !user.is_disabled);
  }, [users, showDisabledUsers]);



  // Email Config Dialog Logic
  const openEmailConfigDialog = () => {
    if (currentEmailConfig) {
        setEmailConfigForm({
            senderEmail: currentEmailConfig.sender_email || '',
            enableCustom: currentEmailConfig.has_config && currentEmailConfig.enabled,
            senderPassword: '', // Always clear for security/re-entry
            smtpHost: '',       // Always clear for re-entry
            smtpPort: '',       // Always clear for re-entry
        });
    } else {
        setEmailConfigForm({
            senderEmail: '',
            senderPassword: '',
            smtpHost: '',
            smtpPort: '',
            enableCustom: false,
        });
    }
    setIsEmailConfigDialogOpen(true);
  };

  const handleSaveEmailConfig = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!adminToken) return;

    let payload: EmailConfigUpdatePayload;

    if (emailConfigForm.enableCustom) {
        if (!emailConfigForm.senderEmail || !emailConfigForm.senderPassword || !emailConfigForm.smtpHost || !emailConfigForm.smtpPort) {
            toast({ title: t('errorTitle'), description: t('adminEmailConfigRequiredFields'), variant: 'destructive' });
            return;
        }
        const port = parseInt(String(emailConfigForm.smtpPort), 10);
        if (isNaN(port) || port < 1 || port > 65535) {
            toast({ title: t('errorTitle'), description: t('adminSmtpPortHint'), variant: 'destructive' }); // Assuming SmtpPortHint also implies valid range
            return;
        }
        payload = {
            sender_email: emailConfigForm.senderEmail,
            sender_password: emailConfigForm.senderPassword,
            smtp_host: emailConfigForm.smtpHost,
            smtp_port: port,
            enable: true,
        };
    } else {
        payload = { enable: false };
    }

    setIsSavingEmailConfig(true);
    try {
        const response = await fetch(`${API_BASE_URL}/admin/email-config`, {
            method: 'PUT',
            headers: { 
                'Authorization': `Bearer ${adminToken}`, 
                'Content-Type': 'application/json',
                'Accept-Language': language,
            },
            body: JSON.stringify(payload),
        });
        const data: AdminApiResponse = await response.json();
        if (!response.ok) {
            if (response.status === 401) {
                toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
                removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
            }
            throw data;
        }
        toast({ title: t('adminEmailConfigSaveSuccess'), description: data.message, variant: 'default', className: "bg-accent text-accent-foreground" });
        fetchEmailConfig(adminToken);
        setIsEmailConfigDialogOpen(false);
    } catch (error: any) {
        handleApiError(error, 'adminEmailConfigSaveError');
    } finally {
        setIsSavingEmailConfig(false);
    }
  };

  const handleTestEmailConfig = async () => {
    if (!adminToken || !currentEmailConfig?.has_config || !currentEmailConfig?.enabled) {
        toast({ title: t('errorTitle'), description: currentEmailConfig?.enabled ? t('adminEmailConfigNoConfig') : t('adminEmailConfigDisabled'), variant: 'destructive' });
        return;
    }
    setIsTestingEmailConfig(true);
    try {
        const response = await fetch(`${API_BASE_URL}/admin/email-config/test`, {
            method: 'POST',
            headers: { 
                'Authorization': `Bearer ${adminToken}`,
                'Accept-Language': language,
            },
        });
        const data: EmailConfigTestResponse = await response.json();
        if (!response.ok) {
            if (response.status === 401) {
                toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
                removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
            }
            throw data;
        }
        toast({ 
            title: t('adminEmailConfigTestSuccess', { recipient: data.recipient || '', time: data.sent_time ? format(new Date(data.sent_time), 'yyyy-MM-dd HH:mm:ss') : '' }), 
            variant: 'default', 
            className: "bg-accent text-accent-foreground" 
        });
    } catch (error: any) {
        handleApiError(error, 'adminEmailConfigTestErrorGeneric'); // handleApiError will use more specific message if error.error matches
    } finally {
        setIsTestingEmailConfig(false);
    }
  };


  if (isLoading || !adminToken) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <TooltipProvider>
    <div className="flex flex-col h-screen">
      <AppHeader title={t('adminDashboardTitle')} />
      <div className="flex-1 overflow-y-auto p-4 md:p-8">
      {adminInfo && (
        <Card className="mb-8 shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center text-xl">
              <Briefcase className="mr-2 h-6 w-6 text-primary"/>
              {t('adminAccountDetailsTitle')}
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div><span className="font-medium text-muted-foreground">{t('username')}:</span> {adminInfo.username}</div>
            <div><span className="font-medium text-muted-foreground">{t('adminEmail')}:</span> {adminInfo.email}</div>
            <div><span className="font-medium text-muted-foreground">{t('adminOrganization')}:</span> {adminInfo.organization}</div>
            <div>
              <span className="font-medium text-muted-foreground">{t('adminAccountExpireTime')}:</span> 
              {format(new Date(adminInfo.expire_time), 'yyyy-MM-dd HH:mm:ss')}
              {adminInfo.expire_time && !adminInfo.is_expired && (
                <span className="ml-1 text-muted-foreground">
                  {t('adminRemainingDays', { days: Math.max(0, differenceInCalendarDays(new Date(adminInfo.expire_time), new Date())) })}
                </span>
              )}
            </div>
            <div>
              <span className="font-medium text-muted-foreground">{t('adminAccountStatus')}:</span> 
              <span className={adminInfo.is_expired ? "text-destructive" : "text-green-600"}>
                {adminInfo.is_expired ? t('adminStatusExpired') : t('adminStatusNotExpired')}
              </span>
            </div>
            <div className="flex items-center space-x-2">
                <span className="font-medium text-muted-foreground">{t('adminSenderEmailLabel')}:</span>
                {isFetchingEmailConfig ? <Loader2 className="h-4 w-4 animate-spin" /> :
                 currentEmailConfig && currentEmailConfig.has_config && currentEmailConfig.enabled ? (
                    <span className="text-primary">{currentEmailConfig.sender_email}</span>
                ) : (
                    <span className="text-muted-foreground italic">{t('adminSystemDefaultSender')}</span>
                )}
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" onClick={openEmailConfigDialog} className="h-6 w-6">
                            <Settings className="h-4 w-4" />
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>{t('configureSenderEmailTooltip')}</p>
                    </TooltipContent>
                </Tooltip>
            </div>
          </CardContent>
        </Card>
      )}

      {userSummary && (
        <Card className="mb-8 shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center text-xl">
                <BarChart3 className="mr-2 h-6 w-6 text-primary"/>
                {t('adminUserSummaryTitle')}
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="p-3 bg-muted/50 rounded-lg">
              <p className="text-sm text-muted-foreground">{t('adminTotalUsers')}</p>
              <p className="text-2xl font-semibold">{userSummary.total_users}</p>
            </div>
            <div className="p-3 bg-muted/50 rounded-lg">
              <p className="text-sm text-muted-foreground">{t('adminActiveUsers')}</p>
              <p className="text-2xl font-semibold">{userSummary.active_users}</p>
            </div>
            <div className="p-3 bg-muted/50 rounded-lg">
              <p className="text-sm text-muted-foreground">{t('adminDisabledUsers')}</p>
              <p className="text-2xl font-semibold">{userSummary.disabled_users}</p>
            </div>
            <div className="p-3 bg-muted/50 rounded-lg">
              <p className="text-sm text-muted-foreground">{t('adminQuotaLimit')}</p>
              <p className="text-2xl font-semibold">{userSummary.quota_limit}</p>
            </div>
            <div className="p-3 bg-muted/50 rounded-lg">
              <p className="text-sm text-muted-foreground">{t('adminQuotaUsed')}</p>
              <p className="text-2xl font-semibold">{userSummary.quota_used}</p>
            </div>
            <div className="p-3 bg-muted/50 rounded-lg">
              <p className="text-sm text-muted-foreground">{t('adminQuotaRemaining')}</p>
              <p className="text-2xl font-semibold text-accent">{userSummary.quota_remaining}</p>
            </div>
          </CardContent>
        </Card>
      )}

      <Card className="shadow-md mb-8">
        <CardHeader className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
            <CardTitle className="flex items-center text-xl">
              <Users className="mr-2 h-6 w-6 text-primary"/>
              {t('adminUsersList')}
            </CardTitle>
            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-disabled-users"
                  checked={showDisabledUsers}
                  onCheckedChange={handleShowDisabledUsersChange}
                  aria-label={t('adminShowDisabledUsers')}
                />
                <Label htmlFor="show-disabled-users" className="whitespace-nowrap">{t('adminShowDisabledUsers')}</Label>
              </div>
              <Button onClick={() => openUserDialog('addUser')}>
                <PlusCircle className="mr-2 h-4 w-4" /> {t('adminAddUser')}
              </Button>
            </div>
        </CardHeader>
        <CardContent>
          {isFetchingUsers ? (
            <div className="flex justify-center items-center py-10">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="ml-2">{t('adminLoadingUsers')}</p>
            </div>
          ) : displayUsers.length === 0 ? (
             <p className="text-center py-10 text-muted-foreground">
               {showDisabledUsers ? t('adminNoUsersFound') : t('adminNoActiveUsersFound')}
             </p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('username')}</TableHead>
                  <TableHead>{t('adminEmail')}</TableHead>
                  <TableHead>{t('adminStatus')}</TableHead>
                  <TableHead>{t('adminCreatedAt')}</TableHead>
                  <TableHead className="text-right">{t('adminActions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {displayUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">{user.username}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${user.is_disabled ? 'bg-destructive/20 text-destructive' : 'bg-green-500/20 text-green-700'}`}>
                        {user.is_disabled ? t('adminStatusDisabled') : t('adminStatusActive')}
                      </span>
                    </TableCell>
                    <TableCell>{format(new Date(user.created_at), 'yyyy-MM-dd HH:mm:ss')}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => openUserDialog('editEmail', user)}>
                            <Edit3 className="mr-2 h-4 w-4" />{t('adminEditEmailTitle')}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => openUserDialog('editPassword', user)}>
                            <KeyRound className="mr-2 h-4 w-4" />{t('adminEditPasswordTitle')}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleToggleUserStatus(user, !user.is_disabled)}>
                            {user.is_disabled ? <UserCheck className="mr-2 h-4 w-4 text-green-600" /> : <UserX className="mr-2 h-4 w-4 text-red-600" />}
                            {user.is_disabled ? t('adminEnableUser') : t('adminDisableUser')}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Quick Navigation Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card className="shadow-md hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/admin/qr_record')}>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <QrCode className="mr-3 h-6 w-6 text-primary"/>
              {t('adminQrRecordsTitle')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-sm">
              {t('adminQrRecordsDescription')}
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-md hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/admin/record')}>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <ListChecks className="mr-3 h-6 w-6 text-primary"/>
              {t('adminRecentRecordsTitle')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-sm">
              {t('adminRecentRecordsDescription')}
            </p>
          </CardContent>
        </Card>
      </div>
      </div>

      {/* User Management Dialogs */}
      <Dialog open={userDialogState.type === 'addUser' && userDialogState.isOpen} onOpenChange={(isOpen) => !isOpen && closeUserDialog()}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('adminAddUser')}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleAddUserSubmit} className="space-y-4">
            <div>
              <Label htmlFor="add-username">{t('username')}</Label>
              <Input id="add-username" value={addUserForm.username} onChange={(e) => setAddUserForm(prev => ({...prev, username: e.target.value}))} required />
            </div>
            <div>
              <Label htmlFor="add-email">{t('adminEmail')}</Label>
              <Input id="add-email" type="email" value={addUserForm.email} onChange={(e) => setAddUserForm(prev => ({...prev, email: e.target.value}))} required />
            </div>
            <div>
              <Label htmlFor="add-password">{t('password')}</Label>
              <Input 
                id="add-password" 
                type="password" 
                value={addUserForm.password} 
                onChange={(e) => {
                  const newPassword = e.target.value;
                  let passError = '';
                  if (newPassword && newPassword.length < 8) {
                      passError = t('adminPasswordMinLengthError');
                  } else if (newPassword && /^\d+$/.test(newPassword)) {
                      passError = t('adminPasswordNumericOnlyError');
                  }

                  setAddUserForm(prev => ({
                      ...prev,
                      password: newPassword,
                      passwordError: passError,
                      confirmPasswordError: (prev.confirmPassword && newPassword !== prev.confirmPassword) ? t('adminPasswordMismatch') : ''
                  }));
                }}
                required 
              />
              {addUserForm.passwordError && <p className="text-sm text-destructive mt-1">{addUserForm.passwordError}</p>}
            </div>
            <div>
              <Label htmlFor="add-confirmPassword">{t('adminConfirmPassword')}</Label>
              <Input 
                id="add-confirmPassword" 
                type="password" 
                value={addUserForm.confirmPassword} 
                onChange={(e) => {
                  const newConfirmPassword = e.target.value;
                  setAddUserForm(prev => ({
                      ...prev,
                      confirmPassword: newConfirmPassword,
                      confirmPasswordError: (prev.password !== newConfirmPassword) ? t('adminPasswordMismatch') : ''
                  }));
                }} 
                required 
              />
              {addUserForm.confirmPasswordError && <p className="text-sm text-destructive mt-1">{addUserForm.confirmPasswordError}</p>}
            </div>
            <DialogFooter>
                <DialogClose asChild><Button type="button" variant="outline">{t('adminCancel')}</Button></DialogClose>
                <Button type="submit" disabled={isSubmittingUserDialog || !!addUserForm.passwordError || !!addUserForm.confirmPasswordError}>
                    {isSubmittingUserDialog && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {t('adminAddUser')}
                </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={userDialogState.type === 'editEmail' && userDialogState.isOpen} onOpenChange={(isOpen) => !isOpen && closeUserDialog()}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('adminEditEmailTitle')} - {userDialogState.type === 'editEmail' && userDialogState.user?.username}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleEditEmailSubmit} className="space-y-4">
            <div>
              <Label htmlFor="edit-email">{t('adminEmail')}</Label>
              <Input id="edit-email" type="text" value={editEmailForm.email} onChange={(e) => setEditEmailForm({ email: e.target.value })} />
              <p className="text-sm text-muted-foreground mt-1">{t('adminEditEmailMultipleHint')}</p>
            </div>
            <DialogFooter>
                <DialogClose asChild><Button type="button" variant="outline">{t('adminCancel')}</Button></DialogClose>
                <Button type="submit" disabled={isSubmittingUserDialog}>
                    {isSubmittingUserDialog && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {t('adminSaveChanges')}
                </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={userDialogState.type === 'editPassword' && userDialogState.isOpen} onOpenChange={(isOpen) => !isOpen && closeUserDialog()}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('adminEditPasswordTitle')} - {userDialogState.type === 'editPassword' && userDialogState.user?.username}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleEditPasswordSubmit} className="space-y-4">
            <div>
              <Label htmlFor="edit-password">{t('adminNewPassword')}</Label>
              <Input 
                id="edit-password" 
                type="password" 
                value={editPasswordForm.password} 
                onChange={(e) => {
                  const newPassword = e.target.value;
                  let passError = '';
                  if (newPassword && newPassword.length < 8) {
                      passError = t('adminPasswordMinLengthError');
                  } else if (newPassword && /^\d+$/.test(newPassword)) {
                      passError = t('adminPasswordNumericOnlyError');
                  }
                  
                  setEditPasswordForm(prev => ({
                      ...prev,
                      password: newPassword,
                      passwordError: passError,
                      confirmPasswordError: (prev.confirmPassword && newPassword !== prev.confirmPassword) ? t('adminPasswordMismatch') : ''
                  }));
                }}
                required 
              />
              {editPasswordForm.passwordError && <p className="text-sm text-destructive mt-1">{editPasswordForm.passwordError}</p>}
            </div>
            <div>
              <Label htmlFor="edit-confirmPassword">{t('adminConfirmPassword')}</Label>
              <Input 
                id="edit-confirmPassword" 
                type="password" 
                value={editPasswordForm.confirmPassword} 
                onChange={(e) => {
                  const newConfirmPassword = e.target.value;
                  setEditPasswordForm(prev => ({
                      ...prev,
                      confirmPassword: newConfirmPassword,
                      confirmPasswordError: (prev.password !== newConfirmPassword) ? t('adminPasswordMismatch') : ''
                  }));
                }}
                required 
              />
              {editPasswordForm.confirmPasswordError && <p className="text-sm text-destructive mt-1">{editPasswordForm.confirmPasswordError}</p>}
            </div>
            <DialogFooter>
                <DialogClose asChild><Button type="button" variant="outline">{t('adminCancel')}</Button></DialogClose>
                <Button type="submit" disabled={isSubmittingUserDialog || !!editPasswordForm.passwordError || !!editPasswordForm.confirmPasswordError}>
                    {isSubmittingUserDialog && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {t('adminSaveChanges')}
                </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={userDialogState.type === 'confirmStatusChange' && userDialogState.isOpen} onOpenChange={(isOpen) => !isOpen && closeUserDialog()}>
        <DialogContent>
            <DialogHeader>
                <DialogTitle className="flex items-center">
                    <ShieldAlert className="mr-2 h-6 w-6 text-yellow-500" />
                    {userDialogState.type === 'confirmStatusChange' && userDialogState.newStatus ? t('adminConfirmDisableUserTitle') : t('adminConfirmEnableUserTitle')}
                </DialogTitle>
                <DialogDescription>
                    {userDialogState.type === 'confirmStatusChange' && 
                     t(userDialogState.newStatus ? 'adminConfirmDisableUserMessage' : 'adminConfirmEnableUserMessage', {username: userDialogState.user?.username || ''})
                    }
                </DialogDescription>
            </DialogHeader>
            <DialogFooter>
                <DialogClose asChild><Button variant="outline">{t('adminCancel')}</Button></DialogClose>
                <Button 
                    onClick={confirmUserStatusChange} 
                    disabled={isSubmittingUserDialog} 
                    variant={userDialogState.type === 'confirmStatusChange' && userDialogState.newStatus ? "destructive" : "default"}
                >
                    {isSubmittingUserDialog && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {t('adminConfirm')}
                </Button>
            </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Email Configuration Dialog */}
      <Dialog open={isEmailConfigDialogOpen} onOpenChange={setIsEmailConfigDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center"><Mail className="mr-2 h-5 w-5" />{t('adminConfigureEmailTitle')}</DialogTitle>
            <DialogDescription>{t('adminEmailConfigNote')}</DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSaveEmailConfig} className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="enable-custom-email"
                checked={emailConfigForm.enableCustom}
                onCheckedChange={(checked) => setEmailConfigForm(prev => ({ ...prev, enableCustom: checked }))}
              />
              <Label htmlFor="enable-custom-email">{t('adminEnableCustomEmailConfig')}</Label>
            </div>

            {emailConfigForm.enableCustom && (
              <>
                <div>
                  <Label htmlFor="sender-email">{t('adminSenderEmailLabel')}</Label>
                  <Input
                    id="sender-email"
                    type="email"
                    value={emailConfigForm.senderEmail}
                    onChange={(e) => setEmailConfigForm(prev => ({ ...prev, senderEmail: e.target.value }))}
                    disabled={!emailConfigForm.enableCustom || isSavingEmailConfig || isTestingEmailConfig}
                  />
                </div>
                <div>
                  <Label htmlFor="sender-password">{t('adminSenderPasswordLabel')}</Label>
                  <Input
                    id="sender-password"
                    type="password"
                    value={emailConfigForm.senderPassword}
                    onChange={(e) => setEmailConfigForm(prev => ({ ...prev, senderPassword: e.target.value }))}
                    disabled={!emailConfigForm.enableCustom || isSavingEmailConfig || isTestingEmailConfig}
                  />
                </div>
                <div>
                  <Label htmlFor="smtp-host">{t('adminSmtpHostLabel')}</Label>
                  <Input
                    id="smtp-host"
                    value={emailConfigForm.smtpHost}
                    onChange={(e) => setEmailConfigForm(prev => ({ ...prev, smtpHost: e.target.value }))}
                    disabled={!emailConfigForm.enableCustom || isSavingEmailConfig || isTestingEmailConfig}
                  />
                </div>
                <div>
                  <Label htmlFor="smtp-port">{t('adminSmtpPortLabel')}</Label>
                  <Input
                    id="smtp-port"
                    type="number"
                    value={emailConfigForm.smtpPort}
                    onChange={(e) => setEmailConfigForm(prev => ({ ...prev, smtpPort: e.target.value }))}
                    min="1"
                    max="65535"
                    disabled={!emailConfigForm.enableCustom || isSavingEmailConfig || isTestingEmailConfig}
                  />
                  <p className="text-xs text-muted-foreground mt-1">{t('adminSmtpPortHint')}</p>
                </div>
              </>
            )}
            <DialogFooter className="sm:justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={handleTestEmailConfig}
                disabled={!emailConfigForm.enableCustom || isSavingEmailConfig || isTestingEmailConfig || !currentEmailConfig?.has_config || !currentEmailConfig?.enabled}
              >
                {isTestingEmailConfig ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Send className="mr-2 h-4 w-4" />}
                {isTestingEmailConfig ? t('adminTestingEmailConfig') : t('adminTestEmailConfigButton')}
              </Button>
              <div className="flex space-x-2">
                <DialogClose asChild>
                  <Button type="button" variant="outline" disabled={isSavingEmailConfig || isTestingEmailConfig}>{t('adminCancel')}</Button>
                </DialogClose>
                <Button type="submit" disabled={isSavingEmailConfig || isTestingEmailConfig}>
                  {isSavingEmailConfig && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isSavingEmailConfig ? t('adminSavingEmailConfig') : t('adminSaveEmailConfigButton')}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <Toaster />
    </div>
    </TooltipProvider>
  );
}
