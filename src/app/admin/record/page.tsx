'use client';

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { getAdminToken, removeAdminToken, getAdminInfo, removeAdminInfo } from '@/lib/admin-token';
import { useToast } from '@/hooks/use-toast';
import { useTranslations } from '@/hooks/use-translations';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Loader2, 
  Search, 
  RefreshCw, 
  Download, 
  ListChecks,
  ArrowLeft
} from 'lucide-react';
import { format } from 'date-fns';
import { Toaster } from '@/components/ui/toaster';
import { AppHeader } from '@/components/app-header';
import type { RecordItem, AdminRecordsResponse, AdminApiResponse, TranslationKey } from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";

const Highlight = ({ text, highlight }: { text: string; highlight: string }) => {
  if (!highlight.trim()) {
    return <span>{text}</span>;
  }
  const regex = new RegExp(`(${highlight})`, 'gi');
  const parts = text.split(regex);
  return (
    <span>
      {parts.map((part, i) =>
        regex.test(part) ? (
          <strong key={i} className="font-bold text-primary">{part}</strong>
        ) : (
          <span key={i}>{part}</span>
        )
      )}
    </span>
  );
};

export default function AdminRecordPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { t, language } = useTranslations();

  const [adminToken, setAdminToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Records state
  const [records, setRecords] = useState<RecordItem[]>([]);
  const [isFetchingRecords, setIsFetchingRecords] = useState(false);
  const [downloadingRecordId, setDownloadingRecordId] = useState<string | null>(null);
  const [recordsSearchQuery, setRecordsSearchQuery] = useState('');

  const handleApiError = useCallback((error: any, defaultMessageKey: TranslationKey = 'adminGenericError') => {
    let message = t(defaultMessageKey);
    if (error instanceof Error) message = error.message;
    
    if (typeof error === 'object' && error !== null && error.error) {
        const errorMap: Record<string, TranslationKey> = {
            "Invalid credentials": 'adminInvalidCredentialsError',
            "Account has expired": 'adminAccountExpiredError',
            "Admin authentication required": 'adminAuthRequiredError',
            "Record not found": "adminRecordNotFound",
        };
        message = t(errorMap[error.error] || defaultMessageKey);
    }
    
    toast({ title: t('errorTitle'), description: message, variant: 'destructive' });
  }, [toast, t]);

  const fetchRecords = useCallback(async (token: string | null) => {
    if (!token) return;
    setIsFetchingRecords(true);
    try {
      const response = await fetch(`${API_BASE_URL}/admin/records`, {
        headers: { 
            'Authorization': `Bearer ${token}`,
            'Accept-Language': language,
        },
      });
      const data: AdminRecordsResponse = await response.json();
      if (!response.ok) {
        if (response.status === 401) {
          toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
          removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
        }
        throw data;
      }
      setRecords(data.records);
    } catch (error: any) {
      handleApiError(error, 'adminFetchingRecordsError');
      setRecords([]);
    } finally {
      setIsFetchingRecords(false);
    }
  }, [router, toast, t, handleApiError, language]);

  const handleDownloadRecord = async (recordUuid: string) => {
    if (!adminToken) return;
    setDownloadingRecordId(recordUuid);
    try {
      const response = await fetch(`${API_BASE_URL}/admin/records/${recordUuid}/download`, {
        headers: { 
          'Authorization': `Bearer ${adminToken}`,
          'Accept-Language': language,
        },
      });
      const data: AdminApiResponse = await response.json();
      if (!response.ok) {
        throw data;
      }
      if (data.signed_url) {
        window.open(data.signed_url, '_blank');
      } else {
        throw new Error('No download URL found in response.');
      }
    } catch (error: any) {
      handleApiError(error, 'adminDownloadError');
    } finally {
      setDownloadingRecordId(null);
    }
  };

  useEffect(() => {
    const token = getAdminToken();
    if (!token) {
      router.push('/admin/login');
    } else {
      setAdminToken(token);
      fetchRecords(token);
      setIsLoading(false);
    }
  }, [router, fetchRecords]);

  const filteredRecords = useMemo(() => {
    if (!recordsSearchQuery) {
      return records;
    }
    return records.filter(record =>
      record.end_user_username.toLowerCase().includes(recordsSearchQuery.toLowerCase()) ||
      record.record_name.toLowerCase().includes(recordsSearchQuery.toLowerCase())
    );
  }, [records, recordsSearchQuery]);

  if (isLoading || !adminToken) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen">
      <AppHeader title={t('adminRecentRecordsTitle')} />
      <div className="flex-1 overflow-y-auto p-4 md:p-8">
        <Card className="shadow-md">
          <CardHeader className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
              <CardTitle className="flex items-center text-xl">
                  <ListChecks className="mr-2 h-6 w-6 text-primary"/>
                  {t('adminRecentRecordsTitle')}
              </CardTitle>
              <div className="flex items-center gap-2 w-full md:w-auto">
                  <div className="relative w-full md:w-64">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                      <Input
                          type="search"
                          placeholder={t('adminSearchRecordsPlaceholder')}
                          value={recordsSearchQuery}
                          onChange={(e) => setRecordsSearchQuery(e.target.value)}
                          className="pl-10"
                      />
                  </div>
                  <Button 
                      variant="outline" 
                      size="icon" 
                      onClick={() => fetchRecords(adminToken)} 
                      disabled={isFetchingRecords}
                      title={t('refreshLocation')} 
                  >
                      {isFetchingRecords ? <Loader2 className="h-5 w-5 animate-spin" /> : <RefreshCw className="h-5 w-5" />}
                      <span className="sr-only">{t('refreshLocation')}</span>
                  </Button>
              </div>
          </CardHeader>
          <CardContent>
            {isFetchingRecords ? (
              <div className="flex justify-center items-center py-10">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="ml-2">{t('adminLoadingRecords')}</p>
              </div>
            ) : filteredRecords.length > 0 ? (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t('username')}</TableHead>
                      <TableHead>{t('adminRecordName')}</TableHead>
                      <TableHead>{t('adminCreatedAt')}</TableHead>
                      <TableHead className="text-right">{t('adminActions')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRecords.map((record, index) => (
                      <TableRow key={`${record.uuid}-${index}`}>
                        <TableCell>
                          <Highlight text={record.end_user_username} highlight={recordsSearchQuery} />
                        </TableCell>
                        <TableCell className="font-medium truncate max-w-xs" title={record.record_name}>
                           <Highlight text={record.record_name} highlight={recordsSearchQuery} />
                        </TableCell>
                        <TableCell>{format(new Date(record.created_at), 'yyyy-MM-dd HH:mm:ss')}</TableCell>
                        <TableCell className="text-right">
                          <Button 
                            size="sm" 
                            onClick={() => handleDownloadRecord(record.uuid)}
                            disabled={downloadingRecordId === record.uuid}
                          >
                            {downloadingRecordId === record.uuid ? 
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> :
                              <Download className="mr-2 h-4 w-4" />
                            }
                            {t('downloadButton')}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </>
            ) : (
              <p className="text-center py-10 text-muted-foreground">{t('adminNoRecordsFound')}</p>
            )}
          </CardContent>
        </Card>
      </div>

      <Toaster />
    </div>
  );
}
