
'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from '@/hooks/use-toast';
import { useTranslations } from '@/hooks/use-translations';
import { Loader2, Languages, UserCog } from 'lucide-react';
import { saveAdminToken, getAdminToken, saveAdminInfo, removeAdminInfo } from '@/lib/admin-token'; // Removed removeAdminInfo as it's not used here
import type { AdminLoginResponse, Language, AdminInfo } from '@/types';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Toaster } from '@/components/ui/toaster';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";

export default function AdminLoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingToken, setIsCheckingToken] = useState(true);
  const router = useRouter();
  const { toast } = useToast();
  const { t, language, setLanguage, isEnabled: isTranslationEnabled } = useTranslations();

  useEffect(() => {
    if (getAdminToken()) {
      router.push('/admin/dashboard');
    } else {
      setIsCheckingToken(false);
    }
  }, [router]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!username || !password) {
      toast({
        title: t('errorTitle'),
        description: t('loginErrorInvalid'),
        variant: "destructive",
      });
      return;
    }
    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/admin/login`, {
        method: 'POST',
        headers: { 
            'Content-Type': 'application/json',
            'Accept-Language': language,
        },
        body: JSON.stringify({ username, password }),
      });
      const data: AdminLoginResponse = await response.json();

      if (!response.ok) {
        let errorMessage = t('adminGenericError');
        if (data.error === "Invalid credentials") {
            errorMessage = t('adminInvalidCredentialsError');
        } else if (data.error === "Account has expired") {
            errorMessage = t('adminAccountExpiredError');
        }
        toast({
          title: t('loginErrorTitle'),
          description: data.error || errorMessage,
          variant: 'destructive',
        });
        setIsLoading(false);
        return;
      }

      if (data.token && data.user_type === 'admin') {
        saveAdminToken(data.token);
        if (data.admin_info) {
          saveAdminInfo(data.admin_info);
        } else {
          // If admin_info is not present, remove any stale info
          removeAdminInfo(); 
        }
        router.push('/admin/dashboard');
      } else {
        toast({
          title: t('loginErrorTitle'),
          description: t('loginErrorMissingToken'),
          variant: 'destructive',
        });
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Admin login error:', error);
      toast({
        title: t('loginErrorTitle'),
        description: t('loginErrorNetwork'),
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  const handleLanguageChange = (lang: Language) => {
    setLanguage(lang);
  };

  if (isCheckingToken) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <>
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Card className="w-full max-w-sm shadow-xl rounded-lg">
          <CardHeader className="text-center relative">
            <CardTitle className="text-2xl font-bold">{t('adminLoginTitle')}</CardTitle>
            <CardDescription>{t('adminLoginDescription')}</CardDescription>
            {isTranslationEnabled && (
              <div className="absolute top-4 right-4">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <Languages className="h-5 w-5" />
                      <span className="sr-only">{t('changeLanguage')}</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleLanguageChange('en')} disabled={language === 'en'}>
                      {t('languageNameEn')}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleLanguageChange('zh')} disabled={language === 'zh'}>
                      {t('languageNameZh')}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleLanguageChange('ko')} disabled={language === 'ko'}>
                      {t('languageNameKo')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">{t('username')}</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder={t('username')}
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  disabled={isLoading}
                  className="bg-input/50"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">{t('password')}</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder={t('password')}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={isLoading}
                  className="bg-input/50"
                />
              </div>
            </CardContent>
            <CardFooter className="flex-col items-stretch">
              <Button 
                type="submit" 
                className="w-full bg-[#09c1ce] hover:bg-[#08AEB9] text-primary-foreground" 
                disabled={isLoading}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t('adminLoginButton')}
              </Button>
              <div className="mt-4 text-center">
                <Link href="/login" className="text-sm text-primary hover:underline flex items-center justify-center">
                  <UserCog className="mr-1 h-4 w-4" />
                  {t('switchToUserLogin')}
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>
      </div>
      <Toaster />
    </>
  );
}
