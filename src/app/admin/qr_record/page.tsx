'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { getAdminToken, removeAdminToken, getAdminInfo, removeAdminInfo } from '@/lib/admin-token';
import { useToast } from '@/hooks/use-toast';
import { useTranslations } from '@/hooks/use-translations';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Loader2,
  Search,
  RefreshCw,
  Download,
  MoreHorizontal,
  Trash2,
  Camera,
  QrCode,
  ArrowLeft,
  ArrowUpDown,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { format } from 'date-fns';
import { Toaster } from '@/components/ui/toaster';
import { AppHeader } from '@/components/app-header';
import type { AdminApiResponse, TranslationKey } from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";

// QR Record specific types
interface QrRecordItem {
  serial_number: string;
  company: string;
  area: string;
  item: string;
  template: string;
  username: string;
  created_at: string;
  updated_at: string;
  required_image_num: number;
  current_image_num: number;
  url?: string; // Download URL
}

interface QrRecordsResponse {
  qr_records: QrRecordItem[];
  error?: string;
}



const Highlight = ({ text, highlight }: { text: string; highlight: string }) => {
  if (!highlight.trim()) {
    return <span>{text}</span>;
  }
  const regex = new RegExp(`(${highlight})`, 'gi');
  const parts = text.split(regex);
  return (
    <span>
      {parts.map((part, i) =>
        regex.test(part) ? (
          <strong key={i} className="font-bold text-primary">{part}</strong>
        ) : (
          <span key={i}>{part}</span>
        )
      )}
    </span>
  );
};

export default function QrRecordPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { t, language } = useTranslations();

  const [adminToken, setAdminToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Records state
  const [records, setRecords] = useState<QrRecordItem[]>([]);
  const [isFetchingRecords, setIsFetchingRecords] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Sorting state
  const [sortField, setSortField] = useState<'created_at' | 'updated_at'>('updated_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Filter state
  const [completionFilter, setCompletionFilter] = useState<'all' | 'completed' | 'incomplete'>('all');
  

  
  // Delete confirmation state
  const [recordToDelete, setRecordToDelete] = useState<QrRecordItem | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  
  // Download state
  const [downloadingRecordSn, setDownloadingRecordSn] = useState<string | null>(null);

  const handleApiError = useCallback((error: any, defaultMessageKey: TranslationKey = 'adminGenericError') => {
    let message = t(defaultMessageKey);
    if (error instanceof Error) message = error.message;
    
    if (typeof error === 'object' && error !== null && error.error) {
        const errorMap: Record<string, TranslationKey> = {
            "Invalid credentials": 'adminInvalidCredentialsError',
            "Account has expired": 'adminAccountExpiredError',
            "Admin authentication required": 'adminAuthRequiredError',
            "Record not found": "adminRecordNotFound",
        };
        message = t(errorMap[error.error] || defaultMessageKey);
    }
    
    toast({ title: t('errorTitle'), description: message, variant: 'destructive' });
  }, [toast, t]);

  const fetchQrRecords = useCallback(async (token: string | null) => {
    if (!token) return;
    setIsFetchingRecords(true);
    try {
      const response = await fetch(`${API_BASE_URL}/admin/qr_records`, {
        headers: { 
            'Authorization': `Bearer ${token}`,
            'Accept-Language': language,
        },
      });
      const data: QrRecordsResponse = await response.json();
      if (!response.ok) {
        if (response.status === 401) {
          toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
          removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
        }
        throw data;
      }
      setRecords(data.qr_records);
    } catch (error: any) {
      handleApiError(error, 'adminFetchingRecordsError');
      setRecords([]);
    } finally {
      setIsFetchingRecords(false);
    }
  }, [router, toast, t, handleApiError, language]);

  const handleSort = (field: 'created_at' | 'updated_at') => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field with default desc direction
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const handleSerialNumberClick = async (serialNumber: string) => {
    if (!adminToken) return;
    try {
      const response = await fetch(`${API_BASE_URL}/image/sn/${serialNumber}`, {
        headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Accept-Language': language,
        },
      });
      const data = await response.json();
      if (!response.ok) {
        if (response.status === 401) {
          toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
          removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
        }
        throw data;
      }

      // Open image in new tab
      if (data.url) {
        window.open(data.url, '_blank');
      } else {
        toast({ title: t('adminNoImagesFound'), description: t('adminNoImagesFoundMessage'), variant: 'default' });
      }
    } catch (error: any) {
      handleApiError(error, 'adminFetchingRecordDetailError');
    }
  };

  const handleDownloadRecord = async (serialNumber: string, downloadUrl?: string) => {
    if (!adminToken) return;
    setDownloadingRecordSn(serialNumber);
    try {
      if (downloadUrl) {
        window.open(downloadUrl, '_blank');
      } else {
        const response = await fetch(`${API_BASE_URL}/admin/qr_records/${serialNumber}/download`, {
          headers: { 
            'Authorization': `Bearer ${adminToken}`,
            'Accept-Language': language,
          },
        });
        const data: AdminApiResponse = await response.json();
        if (!response.ok) {
          throw data;
        }
        if (data.signed_url) {
          window.open(data.signed_url, '_blank');
        } else {
          throw new Error('No download URL found in response.');
        }
      }
    } catch (error: any) {
      handleApiError(error, 'adminDownloadError');
    } finally {
      setDownloadingRecordSn(null);
    }
  };

  const handleDeleteRecord = async () => {
    if (!adminToken || !recordToDelete) return;
    setIsDeleting(true);
    try {
      const response = await fetch(`${API_BASE_URL}/admin/qr_records/${recordToDelete.serial_number}/delete`, {
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Accept-Language': language,
        },
      });
      const data: AdminApiResponse = await response.json();
      if (!response.ok) {
        if (response.status === 401) {
          toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
          removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
        }
        throw data;
      }
      toast({ title: t('adminDeleteRecordSuccess'), description: data.message, variant: 'default', className: "bg-accent text-accent-foreground" });
      fetchQrRecords(adminToken);
      setIsDeleteDialogOpen(false);
      setRecordToDelete(null);
    } catch (error: any) {
      handleApiError(error, 'adminDeleteRecordError');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleRetakePhoto = async (serialNumber: string) => {
    if (!adminToken) return;
    try {
      // Reset the record via API
      const response = await fetch(`${API_BASE_URL}/admin/qr_records/${serialNumber}/reset`, {
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Accept-Language': language,
        },
      });
      const data: AdminApiResponse = await response.json();
      if (!response.ok) {
        if (response.status === 401) {
          toast({ title: t('sessionExpiredTitle'), description: t('adminAuthRequiredError'), variant: "destructive" });
          removeAdminToken(); removeAdminInfo(); router.push('/admin/login'); return;
        }
        throw data;
      }

      // Show success message
      toast({
        title: t('adminRetakePhotoSuccess'),
        description: data.message || t('adminRetakePhotoSuccessMessage'),
        variant: 'default',
        className: "bg-accent text-accent-foreground"
      });

      // Refresh the records list to show updated status
      fetchQrRecords(adminToken);
    } catch (error: any) {
      handleApiError(error, 'adminRetakePhotoError');
    }
  };

  useEffect(() => {
    const token = getAdminToken();
    if (!token) {
      router.push('/admin/login');
    } else {
      setAdminToken(token);
      fetchQrRecords(token);
      setIsLoading(false);
    }
  }, [router, fetchQrRecords]);

  const filteredAndSortedRecords = useMemo(() => {
    if (!records || !Array.isArray(records)) {
      return [];
    }

    // Filter records by search query
    let filtered = records;
    if (searchQuery) {
      filtered = records.filter(record =>
        record.serial_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.area.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.item.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.template.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by completion status
    if (completionFilter === 'completed') {
      filtered = filtered.filter(record => record.current_image_num >= record.required_image_num);
    } else if (completionFilter === 'incomplete') {
      filtered = filtered.filter(record => record.current_image_num < record.required_image_num);
    }

    // Sort records
    return filtered.sort((a, b) => {
      const aDate = new Date(a[sortField]).getTime();
      const bDate = new Date(b[sortField]).getTime();

      if (sortDirection === 'desc') {
        return bDate - aDate;
      } else {
        return aDate - bDate;
      }
    });
  }, [records, searchQuery, sortField, sortDirection, completionFilter]);

  if (isLoading || !adminToken) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen">
      <AppHeader title={t('adminQrRecordsTitle')} />
      <div className="flex-1 overflow-y-auto p-4 md:p-8">
        <Card className="shadow-md">
          <CardHeader className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
              <CardTitle className="flex items-center text-xl">
                <QrCode className="mr-2 h-6 w-6 text-primary"/>
                {t('adminQrRecordsTitle')}
              </CardTitle>
              <div className="flex items-center gap-2 w-full md:w-auto">
                  <div className="relative w-full md:w-64">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                      <Input
                          type="search"
                          placeholder={t('adminSearchQrRecordsPlaceholder')}
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10"
                      />
                  </div>
                  <Select value={completionFilter} onValueChange={(value: 'all' | 'completed' | 'incomplete') => setCompletionFilter(value)}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{t('adminFilterAll')}</SelectItem>
                      <SelectItem value="completed">{t('adminFilterCompleted')}</SelectItem>
                      <SelectItem value="incomplete">{t('adminFilterIncomplete')}</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                      variant="outline"
                      size="icon"
                      onClick={() => fetchQrRecords(adminToken)}
                      disabled={isFetchingRecords}
                      title={t('refreshLocation')}
                  >
                      {isFetchingRecords ? <Loader2 className="h-5 w-5 animate-spin" /> : <RefreshCw className="h-5 w-5" />}
                      <span className="sr-only">{t('refreshLocation')}</span>
                  </Button>
              </div>
          </CardHeader>
          <CardContent>
            {isFetchingRecords ? (
              <div className="flex justify-center items-center py-10">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="ml-2">{t('adminLoadingQrRecords')}</p>
              </div>
            ) : filteredAndSortedRecords.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('adminSerialNumber')}</TableHead>
                    <TableHead>{t('company')}</TableHead>
                    <TableHead>{t('area')}</TableHead>
                    <TableHead>{t('item')}</TableHead>
                    <TableHead>{t('selectTemplate')}</TableHead>
                    <TableHead>{t('username')}</TableHead>
                    <TableHead>{t('adminProgress')}</TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('created_at')}
                        className="h-auto p-0 font-medium hover:bg-transparent"
                      >
                        {t('adminCreatedAt')}
                        {sortField === 'created_at' ? (
                          sortDirection === 'desc' ? <ArrowDown className="ml-1 h-4 w-4" /> : <ArrowUp className="ml-1 h-4 w-4" />
                        ) : (
                          <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                        )}
                      </Button>
                    </TableHead>
                    <TableHead>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('updated_at')}
                        className="h-auto p-0 font-medium hover:bg-transparent"
                      >
                        {t('adminUpdatedAt')}
                        {sortField === 'updated_at' ? (
                          sortDirection === 'desc' ? <ArrowDown className="ml-1 h-4 w-4" /> : <ArrowUp className="ml-1 h-4 w-4" />
                        ) : (
                          <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                        )}
                      </Button>
                    </TableHead>
                    <TableHead className="text-right">{t('adminActions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAndSortedRecords.map((record) => (
                    <TableRow key={record.serial_number}>
                      <TableCell>
                        <Button
                          variant="link"
                          className="p-0 h-auto font-mono text-primary hover:underline"
                          onClick={() => handleSerialNumberClick(record.serial_number)}
                        >
                          <Highlight text={record.serial_number} highlight={searchQuery} />
                        </Button>
                      </TableCell>
                      <TableCell>
                        <Highlight text={record.company} highlight={searchQuery} />
                      </TableCell>
                      <TableCell>
                        <Highlight text={record.area} highlight={searchQuery} />
                      </TableCell>
                      <TableCell>
                        <Highlight text={record.item} highlight={searchQuery} />
                      </TableCell>
                      <TableCell>
                        <Highlight text={record.template} highlight={searchQuery} />
                      </TableCell>
                      <TableCell>
                        <Highlight text={record.username} highlight={searchQuery} />
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-medium">
                          {record.current_image_num}/{record.required_image_num}
                        </div>
                      </TableCell>
                      <TableCell>{format(new Date(record.created_at), 'yyyy-MM-dd HH:mm:ss')}</TableCell>
                      <TableCell>{format(new Date(record.updated_at), 'yyyy-MM-dd HH:mm:ss')}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {record.url && (
                              <DropdownMenuItem onClick={() => handleDownloadRecord(record.serial_number, record.url)}>
                                <Download className="mr-2 h-4 w-4" />{t('downloadButton')}
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem onClick={() => handleRetakePhoto(record.serial_number)}>
                              <Camera className="mr-2 h-4 w-4" />{t('adminRetakePhoto')}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => {
                                setRecordToDelete(record);
                                setIsDeleteDialogOpen(true);
                              }}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />{t('adminDeleteRecord')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p className="text-center py-10 text-muted-foreground">{t('adminNoQrRecordsFound')}</p>
            )}
          </CardContent>
        </Card>
      </div>



      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center text-destructive">
              <Trash2 className="mr-2 h-5 w-5" />
              {t('adminDeleteRecordTitle')}
            </DialogTitle>
            <DialogDescription>
              {t('adminDeleteRecordMessage', { serialNumber: recordToDelete?.serial_number || '' })}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              {t('adminCancel')}
            </Button>
            <Button 
              variant="destructive"
              onClick={handleDeleteRecord}
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('adminDeleteRecord')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Toaster />
    </div>
  );
}
