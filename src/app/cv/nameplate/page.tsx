
'use client';

import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { Upload, Loader2, Download, ArrowLeft, Sheet as SheetIcon, Languages, Expand, CheckCircle, Camera, Images as ImagesIcon, RotateCw, AlertCircle, History, Archive, Trash2 } from 'lucide-react';

import { useAuth } from '@/contexts/auth-context';
import { useTranslations } from '@/hooks/use-translations';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter, CardDescription } from '@/components/ui/card';
import { Toaster } from '@/components/ui/toaster';
import { useToast } from '@/hooks/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogDescription, DialogClose } from '@/components/ui/dialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger, SheetClose } from "@/components/ui/sheet";
import type { ExcelPayload, ExcelResponse, Language, SingleImageProcessResponse, FileLink, DropdownOptions, StagedRecordSummary, StagedRecordDataResponse } from '@/types';
import { useIsMobile } from '@/hooks/use-mobile';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useFitImage } from '@/hooks/use-fit-image';
import { ComboboxInput } from '@/components/combobox-input';
import { Label } from '@/components/ui/label';
import { getHistory, saveHistory } from '@/lib/localstorage-history';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { AppHeader } from '@/components/app-header';


const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";
const HISTORY_LIMIT = 5;
const IDENTIFY_CONFLICTS_VALUE = "[Conflict]";


// Helper functions
const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = error => reject(error);
    });
};

const dataURIToFile = (dataURI: string, fileName: string): File | null => {
    try {
        const arr = dataURI.split(',');
        if (arr.length < 2) return null;
        
        const mimeMatch = arr[0].match(/:(.*?);/);
        if (!mimeMatch) return null;

        const mime = mimeMatch[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);

        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }

        return new File([u8arr], fileName, { type: mime });
    } catch (error) {
        console.error("Error converting data URI to file:", error);
        return null;
    }
};

const toCounterClockwise = (angle: number) => (360 - (angle % 360)) % 360;
const toClockwise = (angle: number) => (360 + angle) % 360;

const AutosizeTextarea = (props: React.TextareaHTMLAttributes<HTMLTextAreaElement>) => {
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    useEffect(() => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto'; // Reset height
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        }
    }, [props.value]);

    return (
        <Textarea
            ref={textareaRef}
            {...props}
            className="w-full resize-none overflow-hidden p-2 text-sm border-none focus:ring-0"
        />
    );
};


export default function CVNameplatePage() {
    const { t, language, setLanguage, isEnabled: isTranslationEnabled } = useTranslations();
    const { toast } = useToast();
    const { token: authToken, logout, isLoading: authIsLoading, isAuthenticated } = useAuth();
    const router = useRouter();
    const isMobile = useIsMobile();
    const [isClient, setIsClient] = useState(false);

    // Form states
    const [company, setCompany] = useState('');
    const [item, setItem] = useState('');
    const [area, setArea] = useState('');
    const [tableData, setTableData] = useState<string[][] | null>(null);
    
    // Image states
    const [itemImageFile, setItemImageFile] = useState<File | null>(null);
    const [itemImagePreviewUrl, setItemImagePreviewUrl] = useState<string | null>(null);
    const [itemImageDataBase64, setItemImageDataBase64] = useState<string | null>(null);
    const [itemImageAngle, setItemImageAngle] = useState(0);

    const [nameplateImageFile, setNameplateImageFile] = useState<File | null>(null);
    const [nameplateImagePreviewUrl, setNameplateImagePreviewUrl] = useState<string | null>(null);
    const [nameplateImageDataBase64, setNameplateImageDataBase64] = useState<string | null>(null);
    const [nameplateImageAngle, setNameplateImageAngle] = useState(0);

    // Dropdown options
    const [dropdownOptions, setDropdownOptions] = useState<DropdownOptions | null>(null);
    const [combinedDropdownOptions, setCombinedDropdownOptions] = useState<DropdownOptions | null>(null);
    const [isLoadingOptions, setIsLoadingOptions] = useState(true);

    // UI states
    const [isProcessing, setIsProcessing] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [generatedFiles, setGeneratedFiles] = useState<FileLink[] | null>(null);
    const [isDownloadDialogOpen, setIsDownloadDialogOpen] = useState(false);
    
    // Conflict states
    const [companyConflict, setCompanyConflict] = useState(false);
    const [itemConflict, setItemConflict] = useState(false);
    
    // Upload mode state
    const [uploadMode, setUploadMode] = useState<'album' | 'camera'>('album');

    // Staging State
    const [isStaging, setIsStaging] = useState(false);
    const [isStagingSheetOpen, setIsStagingSheetOpen] = useState(false);
    const [stagedRecords, setStagedRecords] = useState<StagedRecordSummary[]>([]);
    const [isLoadingStagedRecords, setIsLoadingStagedRecords] = useState(false);
    const [currentRecordUuid, setCurrentRecordUuid] = useState<string | null>(null);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [recordToDelete, setRecordToDelete] = useState<StagedRecordSummary | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);


    useEffect(() => {
      setIsClient(true);
    }, []);

    // Redirect if not authenticated
    useEffect(() => {
        if (isClient && !authIsLoading && !isAuthenticated) {
            router.push('/login');
        }
    }, [isClient, authIsLoading, isAuthenticated, router]);

    const handleLanguageChange = (lang: Language) => {
        setLanguage(lang);
    };

    const handleClearForm = useCallback((showToast = true) => {
        setTableData(null);
        setError(null);
        setIsSubmitting(false);
        setGeneratedFiles(null);
        setIsDownloadDialogOpen(false);
        setCompany('');
        setItem('');
        setArea('');
        setCompanyConflict(false);
        setItemConflict(false);
        setCurrentRecordUuid(null);
        
        if (itemImagePreviewUrl) URL.revokeObjectURL(itemImagePreviewUrl);
        setItemImageFile(null);
        setItemImagePreviewUrl(null);
        setItemImageDataBase64(null);
        setItemImageAngle(0);
        
        if (nameplateImagePreviewUrl) URL.revokeObjectURL(nameplateImagePreviewUrl);
        setNameplateImageFile(null);
        setNameplateImagePreviewUrl(null);
        setNameplateImageDataBase64(null);
        setNameplateImageAngle(0);

        if (showToast) {
            toast({
                title: t('formClearedToastTitle'),
                description: t('formClearedToastDescription'),
                variant: 'default',
                className: "bg-accent text-accent-foreground"
            });
        }
    }, [itemImagePreviewUrl, nameplateImagePreviewUrl, t, toast]);

    const handleImageChange = async (type: 'item' | 'nameplate', event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            const previewUrl = URL.createObjectURL(file);
            const base64 = await fileToBase64(file);

            if (type === 'item') {
                if (itemImagePreviewUrl) URL.revokeObjectURL(itemImagePreviewUrl);
                setItemImageFile(file);
                setItemImagePreviewUrl(previewUrl);
                setItemImageDataBase64(base64);
            } else {
                if (nameplateImagePreviewUrl) URL.revokeObjectURL(nameplateImagePreviewUrl);
                setNameplateImageFile(file);
                setNameplateImagePreviewUrl(previewUrl);
                setNameplateImageDataBase64(base64);
            }
        }
    };

    useEffect(() => {
      async function fetchInitialDropdownOptions() {
          try {
              const staticOptions: DropdownOptions = { 
                company: ["Company Alpha", "Company Beta", "Company Gamma"],
                area: ["Area 1", "Area 2", "Area 3"],
                item: ["SN-00001", "SN-00002", "SN-00003", "SN-00004"],
                status: ["Operational", "Maintenance", "Offline", "Error"],
              };
              setDropdownOptions(staticOptions); 
          } catch (error) {
              console.error("Error fetching dropdown options:", error);
              toast({
                title: t('errorTitle'),
                description: t('optionsLoadErrorDescription'),
                variant: "destructive",
              });
              setDropdownOptions({ company: [], area: [], item: [], status: [] });
          } finally {
              setIsLoadingOptions(false);
          }
      }
      if (isClient) { 
          fetchInitialDropdownOptions();
      }
    }, [toast, t, isClient]); 

    useEffect(() => {
        if (isClient && dropdownOptions) {
            const finalOptions: DropdownOptions = {
                company: [], area: [], item: [], status: []
            };
            const keys: (keyof DropdownOptions)[] = ['company', 'area', 'item'];

            keys.forEach(key => {
                const history = getHistory(key, HISTORY_LIMIT);
                const defaultValues = dropdownOptions[key] || [];
                const valueSet = new Set([...history, ...defaultValues]);
                finalOptions[key] = Array.from(valueSet);
            });
            // status doesn't use history on this page
            finalOptions.status = dropdownOptions.status || [];

            setCombinedDropdownOptions(finalOptions);
        }
    }, [dropdownOptions, isClient]);

    const handleExtractTable = useCallback(async () => {
        if (!itemImageDataBase64 || !nameplateImageDataBase64 || !authToken) {
            toast({ title: t('errorTitle'), description: t('singleImageUploadFirstError'), variant: 'destructive' });
            return;
        }

        setIsProcessing(true);
        setError(null);
        setTableData(null);
        setGeneratedFiles(null);
        setIsDownloadDialogOpen(false);
        setCompany('');
        setItem('');
        setArea('');
        setCompanyConflict(false);
        setItemConflict(false);

        try {
            const payload = {
                images: [
                    {
                        imageData: itemImageDataBase64,
                        angle: toCounterClockwise(itemImageAngle)
                    },
                    {
                        imageData: nameplateImageDataBase64,
                        angle: toCounterClockwise(nameplateImageAngle)
                    }
                ]
            };

            const response = await fetch(`${API_BASE_URL}/process-nameplate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`,
                    'Accept-Language': language,
                },
                body: JSON.stringify(payload),
            });

            const responseData: SingleImageProcessResponse = await response.json();

            if (!response.ok) {
                if (response.status === 401) {
                    toast({ title: t('sessionExpiredTitle'), description: t('sessionExpiredDescription'), variant: "destructive" });
                    logout();
                }
                throw new Error(responseData.error || t('singleImageProcessError'));
            }

            if (responseData.data && Array.isArray(responseData.data)) {
                const filteredData = responseData.data.filter(row => Array.isArray(row) && row.length === 2);
                setTableData(filteredData);
            } else {
                setTableData(null);
            }

            if (responseData.company) {
              if (responseData.company === IDENTIFY_CONFLICTS_VALUE) {
                setCompanyConflict(true);
              } else {
                setCompany(responseData.company);
                setCompanyConflict(false);
              }
            } else { setCompanyConflict(false); }

            if (responseData.item) {
              if (responseData.item === IDENTIFY_CONFLICTS_VALUE) {
                setItemConflict(true);
              } else {
                setItem(responseData.item);
                setItemConflict(false);
              }
            } else { setItemConflict(false); }
            
            if (responseData.area) {
                setArea(responseData.area);
            }
            
            setItemImageAngle(toClockwise(responseData.item_image_angle || 0));
            setNameplateImageAngle(toClockwise(responseData.nameplate_image_angle || 0));

        } catch (err: any) {
            const errorMessage = err.message || 'An unknown error occurred.';
            setError(errorMessage);
            toast({ title: t('errorTitle'), description: errorMessage, variant: 'destructive' });
        } finally {
            setIsProcessing(false);
        }
    }, [itemImageDataBase64, nameplateImageDataBase64, authToken, toast, logout, t, language, itemImageAngle, nameplateImageAngle]);

    const handleTableCellChange = (rowIndex: number, colIndex: number, value: string) => {
        if (!tableData) return;
        const newData = tableData.map((row, rIdx) => {
            if (rIdx === rowIndex) {
                return row.map((cell, cIdx) => (cIdx === colIndex ? value : cell));
            }
            return row;
        });
        setTableData(newData);
    };

    const getPayload = async (): Promise<ExcelPayload | null> => {
        if (!itemImageFile || !itemImageDataBase64 || !nameplateImageFile || !nameplateImageDataBase64) {
            toast({ title: t('errorTitle'), description: t('singleImageNoDataError'), variant: 'destructive' });
            return null;
        }
        try {
             const payload: ExcelPayload = {
                company,
                item,
                area,
                data: tableData || undefined,
                images: [
                    {
                        imageData: itemImageDataBase64,
                        fileName: itemImageFile.name,
                        angle: toCounterClockwise(itemImageAngle),
                    },
                    {
                        imageData: nameplateImageDataBase64,
                        fileName: nameplateImageFile.name,
                        angle: toCounterClockwise(nameplateImageAngle),
                    }
                ],
                template: tableData ? 'nameplate' : undefined, // Only include template if data exists
                ...(currentRecordUuid && { record_uuid: currentRecordUuid }),
            };
            return payload;
        } catch (error) {
            console.error("Error creating payload:", error);
            toast({ title: t('errorTitle'), description: "Failed to prepare data for submission.", variant: "destructive" });
            return null;
        }
    }


    const handleSubmitToExcel = async () => {
        if (!isSubmitActive) {
            toast({ title: t('errorTitle'), description: t('singleImageNoDataError'), variant: 'destructive' });
            return;
        }

        setIsSubmitting(true);

        if (isClient) {
            if (company && company !== IDENTIFY_CONFLICTS_VALUE) saveHistory('company', company, HISTORY_LIMIT);
            if (area && area !== IDENTIFY_CONFLICTS_VALUE) saveHistory('area', area, HISTORY_LIMIT);
            if (item && item !== IDENTIFY_CONFLICTS_VALUE) saveHistory('item', item, HISTORY_LIMIT);
        }

        try {
            const payload = await getPayload();
            if (!payload) {
                setIsSubmitting(false);
                return;
            }

            const response = await fetch(`${API_BASE_URL}/generate-excel`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`,
                    'Accept-Language': language,
                },
                body: JSON.stringify(payload),
            });

            const responseData: ExcelResponse = await response.json();

            if (!response.ok) {
                throw new Error(responseData.message || `HTTP error! status: ${response.status}`);
            }

            if (!responseData.files || responseData.files.length === 0) {
                throw new Error(t('singleImageInvalidExcelApiError'));
            }

            setGeneratedFiles(responseData.files);
            setIsDownloadDialogOpen(true);
            setCurrentRecordUuid(null); // Clear UUID after successful submission
            
        } catch (err: any) {
            const errorMessage = err.message || t('submitError');
            toast({ title: t('submitError'), description: errorMessage, variant: "destructive" });
        } finally {
            setIsSubmitting(false);
        }
    };
    
    // Staging Logic
    const isFormReadyForStaging = useMemo(() => {
        return !!company && !!item && !!area && (!!itemImageFile || !!nameplateImageFile);
    }, [company, item, area, itemImageFile, nameplateImageFile]);

    const handleStageData = async () => {
        if (!isFormReadyForStaging) {
          toast({ title: t('missingInfoTitle'), description: "Company, Item, Area and at least one image are required to stage data.", variant: "destructive" });
          return;
        }
        setIsStaging(true);

        const payload = await getPayload();
        if (!payload) {
            setIsStaging(false);
            return;
        }
        delete payload.template;

        try {
            const response = await fetch(`${API_BASE_URL}/nameplate/storage`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${authToken}`,
                  'Accept-Language': language,
                },
                body: JSON.stringify(payload),
            });
            if (response.status === 401) { logout(); return; }
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || t('stageError'));
            }
            toast({ title: t('stageSuccessTitle'), description: t('stageSuccessDescription'), variant: 'default', className: "bg-accent text-accent-foreground"});
            handleClearForm(false);
        } catch (error) {
            const message = error instanceof Error ? error.message : t('stageError');
            toast({ title: t('stageErrorTitle'), description: message, variant: "destructive" });
        } finally {
            setIsStaging(false);
        }
    };

    const fetchStagedRecords = useCallback(async () => {
        if (!authToken) return;
        setIsLoadingStagedRecords(true);
        try {
            const response = await fetch(`${API_BASE_URL}/nameplate/storage`, {
                headers: { 'Authorization': `Bearer ${authToken}`, 'Accept-Language': language },
            });
            if (response.status === 401) { logout(); return; }
            const data = await response.json();
            if (!response.ok) throw new Error(data.error || t('fetchStagedRecordsError'));
            setStagedRecords(data.data || []);
        } catch (error) {
            toast({ title: t('errorTitle'), description: (error as Error).message, variant: 'destructive' });
            setStagedRecords([]);
        } finally {
            setIsLoadingStagedRecords(false);
        }
    }, [authToken, language, logout, t, toast]);

    const loadStagedRecord = useCallback(async (uuid: string) => {
        if (!authToken) return;
        handleClearForm(false);
        toast({ title: t('loading'), description: t('loadingStagedRecords'), variant: "default" });
        try {
            const response = await fetch(`${API_BASE_URL}/nameplate/storage/${uuid}`, {
                headers: { 'Authorization': `Bearer ${authToken}`, 'Accept-Language': language },
            });
            if (response.status === 401) { logout(); return; }
            const data: StagedRecordDataResponse = await response.json();
            if (!response.ok) throw new Error(data.error || t('loadStagedRecordError'));

            setCompany(data.company || '');
            setItem(data.item || '');
            setArea(data.area || '');
            setTableData(data.data || null);

            // Item Image (first in array)
            if(data.images && data.images.length > 0) {
                const itemImgData = data.images[0];
                const itemFile = dataURIToFile(itemImgData.imageData, itemImgData.fileName);
                setItemImageFile(itemFile);
                setItemImagePreviewUrl(itemFile ? URL.createObjectURL(itemFile) : null);
                setItemImageDataBase64(itemImgData.imageData);
                setItemImageAngle(toClockwise(itemImgData.angle || 0));
            }
            // Nameplate Image (second in array)
            if(data.images && data.images.length > 1) {
                const nameplateImgData = data.images[1];
                const nameplateFile = dataURIToFile(nameplateImgData.imageData, nameplateImgData.fileName);
                setNameplateImageFile(nameplateFile);
                setNameplateImagePreviewUrl(nameplateFile ? URL.createObjectURL(nameplateFile) : null);
                setNameplateImageDataBase64(nameplateImgData.imageData);
                setNameplateImageAngle(toClockwise(nameplateImgData.angle || 0));
            }

            setCurrentRecordUuid(uuid);
            setIsStagingSheetOpen(false);
            toast({ title: t('successTitle'), description: t('loadRecordSuccess'), variant: 'default', className: "bg-accent text-accent-foreground"});

        } catch (error) {
            toast({ title: t('errorTitle'), description: (error as Error).message, variant: 'destructive' });
        }
    }, [authToken, language, logout, t, toast, handleClearForm]);

    const openDeleteDialog = (record: StagedRecordSummary) => {
        setRecordToDelete(record);
        setIsDeleteDialogOpen(true);
    };

    const handleDeleteStagedRecord = async () => {
        if (!recordToDelete || !authToken) return;
        setIsDeleting(true);
        try {
            const response = await fetch(`${API_BASE_URL}/nameplate/storage/${recordToDelete.uuid}`, {
                method: 'DELETE',
                headers: { 'Authorization': `Bearer ${authToken}`, 'Accept-Language': language },
            });
            if (response.status === 401) { logout(); return; }
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || t('stageDeleteError'));
            }
            setStagedRecords(prev => prev.filter(r => r.uuid !== recordToDelete.uuid));
            toast({ title: t('successTitle'), description: t('deleteRecordSuccess'), variant: 'default', className: "bg-accent text-accent-foreground"});
        } catch (error) {
            toast({ title: t('errorTitle'), description: (error as Error).message, variant: "destructive" });
        } finally {
            setIsDeleting(false);
            setIsDeleteDialogOpen(false);
            setRecordToDelete(null);
        }
    };


    const isSubmitActive = useMemo(() => {
        return !!itemImageFile && !!nameplateImageFile && !!company && !!item && !!area && !companyConflict && !itemConflict && !isProcessing && !isSubmitting;
    }, [itemImageFile, nameplateImageFile, company, item, area, companyConflict, itemConflict, isProcessing, isSubmitting]);
    
    if (authIsLoading || !isAuthenticated) {
        return (
            <div className="flex min-h-screen items-center justify-center bg-background">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
            </div>
        );
    }
    
    const ImageUpload = ({
        type,
        title,
        imagePreviewUrl,
        angle,
        setAngle,
    }: {
        type: 'item' | 'nameplate';
        title: string;
        imagePreviewUrl: string | null;
        angle: number;
        setAngle: (angle: number) => void;
    }) => {
        const imageContainerRef = useRef<HTMLDivElement>(null);
        const { transform } = useFitImage(imageContainerRef, angle);
        const [isFullScreen, setIsFullScreen] = useState(false);
    
        return (
            <Card>
                <CardHeader>
                    <CardTitle className='flex items-center justify-between'>
                        {title}
                        {isMobile && type === 'item' && (
                            <ToggleGroup type="single" value={uploadMode} onValueChange={(value) => {if (value) setUploadMode(value as 'album' | 'camera')}} className="gap-0.5">
                                <ToggleGroupItem value="album" aria-label="Select from album" className="px-2.5">
                                    <ImagesIcon className="h-5 w-5" />
                                </ToggleGroupItem>
                                <ToggleGroupItem value="camera" aria-label="Use camera" className="px-2.5">
                                    <Camera className="h-5 w-5" />
                                </ToggleGroupItem>
                            </ToggleGroup>
                         )}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="relative flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg bg-muted/50 hover:bg-muted">
                        <div ref={imageContainerRef} className="w-full h-full flex items-center justify-center overflow-hidden">
                        {imagePreviewUrl ? (
                            <>
                                <div className="absolute top-2 right-2 z-10 flex flex-col space-y-1">
                                    <Button type="button" variant="ghost" size="icon" className="h-7 w-7 text-white bg-black/50 hover:bg-black/75" onClick={(e) => { e.stopPropagation(); setIsFullScreen(true); }}>
                                        <Expand className="h-4 w-4" />
                                    </Button>
                                    <Button type="button" variant="ghost" size="icon" className="h-7 w-7 text-white bg-black/50 hover:bg-black/75" onClick={(e) => { e.stopPropagation(); setAngle((prev) => (prev + 90) % 360); }}>
                                        <RotateCw className="h-4 w-4" />
                                    </Button>
                                </div>
                                <label htmlFor={`image-upload-input-${type}`} className="w-full h-full flex items-center justify-center cursor-pointer">
                                    <div style={{transform, transition: 'transform 0.3s ease', width: '100%', height: '100%', position: 'relative'}}>
                                        <Image src={imagePreviewUrl} alt={`${type} image preview`} fill style={{objectFit: 'contain'}} data-ai-hint="table document" />
                                    </div>
                                </label>
                            </>
                        ) : (
                            <label htmlFor={`image-upload-input-${type}`} className="w-full h-full flex flex-col items-center justify-center cursor-pointer">
                                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                    <Upload className="w-8 h-8 mb-4 text-muted-foreground" />
                                    <p className="mb-2 text-sm text-muted-foreground">
                                        <span className="font-semibold">{t('singleImageClickToUpload')}</span> {t('singleImageDragAndDrop')}
                                    </p>
                                    <p className="text-xs text-muted-foreground">{t('singleImageFormats')}</p>
                                </div>
                            </label>
                        )}
                        </div>
                        <input id={`image-upload-input-${type}`} type="file" className="hidden" accept="image/*" onChange={(e) => handleImageChange(type, e)} {...(uploadMode === 'camera' && { capture: 'environment' })} />
                    </div>
                    {isFullScreen && (
                        <Dialog open={isFullScreen} onOpenChange={setIsFullScreen}>
                            <DialogContent className="p-0 m-0 w-screen h-screen max-w-none border-none bg-black/80">
                                <DialogHeader className="sr-only"><DialogTitle>{t('imagePreviewAlt')}</DialogTitle></DialogHeader>
                                {imagePreviewUrl && (
                                    <div className="w-full h-full flex items-center justify-center p-4" onClick={() => setIsFullScreen(false)}>
                                         <div style={{transform, transition: 'transform 0.3s ease', width: '100%', height: '100%', position: 'relative'}}>
                                            <Image src={imagePreviewUrl} alt="Full screen preview" fill style={{objectFit: 'contain'}} />
                                        </div>
                                    </div>
                                )}
                            </DialogContent>
                        </Dialog>
                    )}
                </CardContent>
            </Card>
        );
    };

    return (
        <TooltipProvider>
        <div className="flex flex-col h-screen">
            <AppHeader title={t('extractTableFromImage')} />
            
            <div className="flex-1 overflow-y-auto p-4 md:p-8">
            <main className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-6">
                     <ImageUpload type="item" title={t('itemImageTitle')} imagePreviewUrl={itemImagePreviewUrl} angle={itemImageAngle} setAngle={setItemImageAngle} />
                     <ImageUpload type="nameplate" title={t('nameplateImageTitle')} imagePreviewUrl={nameplateImagePreviewUrl} angle={nameplateImageAngle} setAngle={setNameplateImageAngle} />
                </div>

                <div className="space-y-6">
                     <Card>
                        <CardHeader>
                            <CardTitle>{t('headerInfo')}</CardTitle>
                        </CardHeader>
                        <CardContent className="grid grid-cols-1 gap-4">
                           {isLoadingOptions || !combinedDropdownOptions ? (
                                <>
                                    <div className="space-y-2"> <Skeleton className="h-4 w-1/4" /> <Skeleton className="h-10 w-full" /> </div>
                                    <div className="space-y-2"> <Skeleton className="h-4 w-1/4" /> <Skeleton className="h-10 w-full" /> </div>
                                    <div className="space-y-2"> <Skeleton className="h-4 w-1/4" /> <Skeleton className="h-10 w-full" /> </div>
                                </>
                           ) : (
                            <>
                                <div className="space-y-1">
                                    <div className="flex items-center">
                                        <Label htmlFor="item">{t('item')}</Label>
                                        {itemConflict && (
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <AlertCircle className="h-4 w-4 text-destructive inline-block ml-1 cursor-help" />
                                                </TooltipTrigger>
                                                <TooltipContent><p>{t('identifyConflictMessage')}</p></TooltipContent>
                                            </Tooltip>
                                        )}
                                    </div>
                                    <ComboboxInput options={combinedDropdownOptions.item} value={item} 
                                        onChange={(newValue) => { setItem(newValue); setItemConflict(false); }} 
                                        placeholder={t('selectOrEnter')} inputPlaceholder={t('searchOrEnter')} 
                                        disabled={isProcessing || isSubmitting} aria-label={t('item')} />
                                </div>
                                <div className="space-y-1">
                                    <div className="flex items-center">
                                        <Label htmlFor="company">{t('company')}</Label>
                                        {companyConflict && (
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <AlertCircle className="h-4 w-4 text-destructive inline-block ml-1 cursor-help" />
                                                </TooltipTrigger>
                                                <TooltipContent><p>{t('identifyConflictMessage')}</p></TooltipContent>
                                            </Tooltip>
                                        )}
                                    </div>
                                    <ComboboxInput options={combinedDropdownOptions.company} value={company} 
                                        onChange={(newValue) => { setCompany(newValue); setCompanyConflict(false); }} 
                                        placeholder={t('selectOrEnter')} inputPlaceholder={t('searchOrEnter')} 
                                        disabled={isProcessing || isSubmitting} aria-label={t('company')} />
                                </div>
                                <div className="space-y-1">
                                    <Label htmlFor="area">{t('area')}</Label>
                                    <ComboboxInput options={combinedDropdownOptions.area} value={area} 
                                        onChange={setArea} 
                                        placeholder={t('selectOrEnter')} inputPlaceholder={t('searchOrEnter')} 
                                        disabled={isProcessing || isSubmitting} aria-label={t('area')} />
                                </div>
                            </>
                           )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>{t('singleImageEditTitle')}</CardTitle>
                        </CardHeader>
                        <CardContent className="min-h-[24rem] overflow-auto">
                            {isProcessing && (
                                <div className="flex items-center justify-center h-full min-h-[20rem]">
                                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                                </div>
                            )}
                            {error && !isProcessing && (
                                <Alert variant="destructive">
                                    <AlertTitle>{t('errorTitle')}</AlertTitle>
                                    <AlertDescription>{error}</AlertDescription>
                                </Alert>
                            )}
                            {tableData && !isProcessing && (
                                <div className="overflow-x-auto">
                                    <Table>
                                        <TableBody>
                                            {tableData.map((row, rowIndex) => (
                                                <TableRow key={rowIndex}>
                                                    {row.map((cell, colIndex) => (
                                                        <TableCell key={colIndex} className="p-0">
                                                             <AutosizeTextarea
                                                                value={cell}
                                                                onChange={(e) => handleTableCellChange(rowIndex, colIndex, e.target.value)}
                                                            />
                                                        </TableCell>
                                                    ))}
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </div>
                            )}
                            {!tableData && !isProcessing && !error && (
                                <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground min-h-[20rem]">
                                    <SheetIcon className="h-12 w-12 mb-4" />
                                    <p>{t('singleImagePlaceholder1')}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                    <div className="flex flex-col sm:flex-row gap-2">
                        <Button
                            type="button"
                            variant={isFormReadyForStaging ? "default" : "outline"}
                            onClick={handleStageData}
                            disabled={!isFormReadyForStaging || isStaging || isSubmitting || isProcessing}
                            className={cn(isFormReadyForStaging && "bg-green-600 hover:bg-green-700")}
                        >
                            {isStaging ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Archive className="mr-2 h-4 w-4" />}
                            {isStaging ? t('stagingData') : t('stageButton')}
                        </Button>
                        <Button onClick={handleExtractTable} disabled={!itemImageFile || !nameplateImageFile || isProcessing || isSubmitting} className="w-full">
                            {isProcessing ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    {t('singleImageExtractingButton')}
                                </>
                            ) : (
                                t('singleImageExtractButton')
                            )}
                        </Button>
                        <Button onClick={handleSubmitToExcel} disabled={!isSubmitActive} className="w-full">
                        {isSubmitting ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    {t('singleImageGeneratingButton')}
                                </>
                            ) : (
                                <>
                                    <Download className="mr-2 h-4 w-4" />
                                    {t('singleImageGenerateButton')}
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </main>
            </div>
            <Toaster />
            
            <Dialog open={isDownloadDialogOpen} onOpenChange={setIsDownloadDialogOpen}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      {t('generationSuccessTitle')}
                  </DialogTitle>
                  <DialogDescription>{t('downloadFilesDescription')}</DialogDescription>
                </DialogHeader>
                <div className="py-4 space-y-4">
                  <ul className="space-y-3">
                      {generatedFiles?.map((file, index) => (
                      <li key={index} className="flex items-center justify-between p-3 rounded-lg border bg-muted/50">
                          <span className="font-medium truncate" title={decodeURIComponent(file.filename)}>{decodeURIComponent(file.filename)}</span>
                          <Button asChild size="sm">
                              <a href={file.url} download={decodeURIComponent(file.filename)} target="_blank" rel="noopener noreferrer">
                                  <Download className="mr-2 h-4 w-4" />
                                  {t('downloadButton')}
                              </a>
                          </Button>
                      </li>
                      ))}
                  </ul>
                  <p className="text-xs text-muted-foreground text-center">{t('downloadLinkExpiration')}</p>
                </div>
                <DialogFooter>
                    <DialogClose asChild>
                        <Button variant="secondary">{t('closeButton')}</Button>
                    </DialogClose>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
              <DialogContent>
                  <DialogHeader>
                      <DialogTitle>{t('deleteStagedRecordTitle')}</DialogTitle>
                      <DialogDescription>
                          {t('deleteStagedRecordMessage', { recordName: recordToDelete?.record_name || '' })}
                      </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                      <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)} disabled={isDeleting}>
                          {t('adminCancel')}
                      </Button>
                      <Button variant="destructive" onClick={handleDeleteStagedRecord} disabled={isDeleting}>
                          {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                          {t('confirmDelete')}
                      </Button>
                  </DialogFooter>
              </DialogContent>
            </Dialog>

        </div>
        </TooltipProvider>
    );
}
