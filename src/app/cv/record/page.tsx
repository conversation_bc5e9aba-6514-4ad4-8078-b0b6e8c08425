
'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Plus, Download, Loader2, Languages, Images, X, LogOut, RefreshCw, Settings, RotateCcw, AlertCircle, CheckCircle, Sheet as SheetIcon, History, Archive, Trash2, Camera, Images as ImagesIcon, RotateCw } from 'lucide-react';
import { useRouter } from 'next/navigation'; 
import Link from 'next/link';

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { ComboboxInput } from "@/components/combobox-input";
import { ImageUploadCard } from "@/components/image-upload-card";
import { Toaster } from "@/components/ui/toaster";
import { useToast } from "@/hooks/use-toast";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useIsMobile } from '@/hooks/use-mobile';
import type { SingleImageData, ExcelPayload, DropdownOptions, Language, ProcessImagePayload, ProcessImageResponse, ImageResult, ExcelResponse, FileLink, StagedRecordsListResponse, StagedRecordSummary, StagedRecordDataResponse, Template, GetTemplateResponse } from '@/types';
import { useTranslations } from '@/hooks/use-translations';
import { Skeleton } from "@/components/ui/skeleton";
import { getHistory, saveHistory } from '@/lib/localstorage-history';
import { useAuth } from '@/contexts/auth-context'; 
import { cn } from '@/lib/utils';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger, SheetClose } from "@/components/ui/sheet";
import { Switch } from '@/components/ui/switch';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AppHeader } from '@/components/app-header';


const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";

const DEFAULT_IMAGES = 3;
const MIN_IMAGES = 1;
const HISTORY_LIMIT = 5;
const IDENTIFY_CONFLICTS_VALUE = "[Conflict]";

const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = error => reject(error);
    });
};

const dataURIToFile = (dataURI: string, fileName: string): File | null => {
    try {
        const arr = dataURI.split(',');
        if (arr.length < 2) return null;
        
        const mimeMatch = arr[0].match(/:(.*?);/);
        if (!mimeMatch) return null;

        const mime = mimeMatch[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);

        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }

        return new File([u8arr], fileName, { type: mime });
    } catch (error) {
        console.error("Error converting data URI to file:", error);
        return null;
    }
};

const toCounterClockwise = (angle: number) => (360 - (angle % 360)) % 360;
const toClockwise = (angle: number) => (360 - (angle % 360)) % 360;

function CVRecordPage() {
  const { t, language, setLanguage, isEnabled: isTranslationEnabled } = useTranslations();
  const { toast } = useToast();
  const { token: authToken, logout, isLoading: authIsLoading, isAuthenticated } = useAuth(); 
  const router = useRouter(); 
  const isMobile = useIsMobile();

  const [company, setCompany] = useState('');
  const [item, setItem] = useState('');
  const [area, setArea] = useState('');
  const [images, setImages] = useState<SingleImageData[]>([]);
  const [dropdownOptions, setDropdownOptions] = useState<DropdownOptions | null>(null);
  const [combinedDropdownOptions, setCombinedDropdownOptions] = useState<DropdownOptions | null>(null);
  const [isLoadingOptions, setIsLoadingOptions] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isBatchProcessing, setIsBatchProcessing] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [isSubmissionSuccessful, setIsSubmissionSuccessful] = useState(false);
  const [generatedFiles, setGeneratedFiles] = useState<FileLink[]>([]);
  const [isDownloadDialogOpen, setIsDownloadDialogOpen] = useState(false);
  
  // Upload mode
  const [uploadMode, setUploadMode] = useState<'album' | 'camera'>('album');

  // Conflict states
  const [companyConflict, setCompanyConflict] = useState(false);
  const [itemConflict, setItemConflict] = useState(false);
  
  // Staging State
  const [isStaging, setIsStaging] = useState(false);
  const [isStagingSheetOpen, setIsStagingSheetOpen] = useState(false);
  const [stagedRecords, setStagedRecords] = useState<StagedRecordSummary[]>([]);
  const [isLoadingStagedRecords, setIsLoadingStagedRecords] = useState(false);
  const [currentRecordUuid, setCurrentRecordUuid] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<StagedRecordSummary | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Template State
  const [templates, setTemplates] = useState<Template[]>([]);
  const [selectedTemplateName, setSelectedTemplateName] = useState<string>('');
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(true);


  const MAX_IMAGES = parseInt(process.env.NEXT_PUBLIC_MAX_IMAGES || '8', 10);

  const initializeImages = useCallback((count: number = DEFAULT_IMAGES) => {
      const generateId = () => typeof crypto !== 'undefined' && crypto.randomUUID ? crypto.randomUUID() : `initial-${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
      
      const newImages = Array.from({ length: count }, (_, i) => ({
        id: generateId(),
        file: null,
        previewUrl: null,
        isProcessing: false,
        needsProcessing: false,
        error: null,
        angle: 0,
        status: '',
        isStatusFixed: false,
      }));
      setImages(newImages);
  }, []);

  const handleTemplateChange = (templateName: string) => {
    setSelectedTemplateName(templateName);
    const template = templates.find(p => p.name === templateName);

    setImages(prevImages => {
        // Fallback to default if template not found or name is empty
        const newCount = template ? template.images_number : prevImages.length;
        const fixedStatuses = template ? template.fixed_status : [];

        const updatedImages = [...prevImages];

        // Adjust array size
        if (updatedImages.length > newCount) {
            // Truncate if new count is smaller, revoking object URLs for removed images
            const removedImages = updatedImages.splice(newCount);
            removedImages.forEach(img => {
                if (img.previewUrl) URL.revokeObjectURL(img.previewUrl);
            });
        } else if (updatedImages.length < newCount) {
            // Add new empty slots if new count is larger
            const generateId = () => typeof crypto !== 'undefined' && crypto.randomUUID ? crypto.randomUUID() : `new-${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
            const slotsToAdd = newCount - updatedImages.length;
            for (let i = 0; i < slotsToAdd; i++) {
                updatedImages.push({
                    id: generateId(),
                    file: null,
                    previewUrl: null,
                    status: '',
                    isProcessing: false,
                    needsProcessing: false,
                    error: null,
                    angle: 0,
                    isStatusFixed: false,
                });
            }
        }

        // Apply fixed statuses from template
        return updatedImages.map((img, i) => {
            const hasFixedStatus = fixedStatuses.length > i && fixedStatuses[i] !== '';
            // If switching TO a template, apply its status.
            // If switching away (templateName is empty/invalid), keep existing status.
            let newStatus = img.status;
            if (template) {
                newStatus = hasFixedStatus ? fixedStatuses[i] : '';
            }
            
            return {
                ...img,
                status: newStatus,
                isStatusFixed: hasFixedStatus,
            };
        });
    });
};


  // Effect to reset submission status on any form data change
  useEffect(() => {
    setIsSubmissionSuccessful(false);
    // Do not clear generatedFiles here, so the "View Downloads" button can work after form edits.
    // generatedFiles will be cleared on new submission or form clear.
  }, [company, item, area, images, selectedTemplateName]);


  const fetchTemplates = useCallback(async () => {
    if (!authToken) return;
    setIsLoadingTemplates(true);
    try {
        const response = await fetch(`${API_BASE_URL}/get_template`, {
            headers: { 
                'Authorization': `Bearer ${authToken}`,
                'Accept-Language': language,
            },
        });
        if (response.status === 401) {
            toast({ title: t('sessionExpiredTitle'), description: t('sessionExpiredDescription'), variant: "destructive" });
            logout();
            return;
        }
        const data: GetTemplateResponse = await response.json();
        if (!response.ok) {
            throw new Error(data.error || `HTTP error! status: ${response.status}`);
        }
        
        if (data.template_list && data.template_list.length > 0) {
          setTemplates(data.template_list);
        } else {
          setTemplates([]);
        }
        initializeImages(); 
    } catch (error) {
        console.error("Error fetching templates:", error);
        setTemplates([]);
        initializeImages(); // Initialize with default on error
        const errorMessage = error instanceof Error ? error.message : t('fetchTemplateError');
        toast({ title: t('errorTitle'), description: errorMessage, variant: 'destructive' });
    } finally {
        setIsLoadingTemplates(false);
    }
  }, [authToken, logout, t, toast, language, initializeImages]);

    const fetchStagedRecords = useCallback(async () => {
        if (!authToken) return;
        setIsLoadingStagedRecords(true);
        try {
            const response = await fetch(`${API_BASE_URL}/record/storage`, {
                headers: { 
                    'Authorization': `Bearer ${authToken}`,
                    'Accept-Language': language,
                },
            });
            if (response.status === 401) {
                toast({ title: t('sessionExpiredTitle'), description: t('sessionExpiredDescription'), variant: "destructive" });
                logout(); return;
            }
            const data: StagedRecordsListResponse = await response.json();
            if (!response.ok) throw new Error(data.error || t('fetchStagedRecordsError'));
            
            setStagedRecords(data.data || []);
        } catch (error) {
            console.error("Error fetching staged records:", error);
            const message = error instanceof Error ? error.message : String(error);
            toast({ title: t('errorTitle'), description: message, variant: 'destructive' });
            setStagedRecords([]);
        } finally {
            setIsLoadingStagedRecords(false);
        }
    }, [authToken, logout, t, toast, language]);

    const handleClearForm = useCallback((showToast = true) => {
    setCompany('');
    setItem('');
    setArea('');
    setIsSubmissionSuccessful(false);
    setGeneratedFiles([]);
    setIsDownloadDialogOpen(false);
    setCurrentRecordUuid(null);
    setSelectedTemplateName(''); // Clear selected template
    
    // Clear images and re-initialize with default count
    images.forEach(img => {
      if (img.previewUrl) {
        URL.revokeObjectURL(img.previewUrl);
      }
    });
    initializeImages();

    setCompanyConflict(false);
    setItemConflict(false);

    if (showToast) {
      toast({ 
        title: t('formClearedToastTitle'), 
        description: t('formClearedToastDescription'), 
        variant: 'default', 
        className: "bg-accent text-accent-foreground"
      });
    }
    }, [images, initializeImages, toast, t]);

    const setImagesAndUpdateNumbers = (newImagesOrCallback: React.SetStateAction<SingleImageData[]>) => {
      setImages(newImagesOrCallback);
    };

    const loadStagedRecord = useCallback(async (uuid: string) => {
        if (!authToken) return;
        handleClearForm(false); // Start with a clean slate, no toast
        toast({ title: t('loading'), description: t('loadingStagedRecords'), variant: "default" });
        try {
            const response = await fetch(`${API_BASE_URL}/record/storage/${uuid}`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Accept-Language': language,
                },
            });
            if (response.status === 401) {
                toast({ title: t('sessionExpiredTitle'), description: t('sessionExpiredDescription'), variant: "destructive" });
                logout(); return;
            }
            const data: StagedRecordDataResponse = await response.json();
            if (!response.ok) throw new Error(data.error || t('loadStagedRecordError'));

            // Populate form fields
            setCompany(data.company || '');
            setItem(data.item || '');
            setArea(data.area || '');
            
            // Check if the saved template still exists. If not, prompt user to select one.
            const savedTemplateExists = templates.some(p => p.name === data.template);
            const templateToSelect = savedTemplateExists ? data.template : '';
            setSelectedTemplateName(templateToSelect || '');

            if (data.template && !savedTemplateExists) {
                toast({
                    title: "Template Not Found",
                    description: `The saved template '${data.template}' could not be found. Please select a new one.`,
                    variant: 'destructive',
                });
            }

            // Process images
            const loadedImages = await Promise.all((data.images || []).map(async (imgData, index) => {
                const newId = `loaded-${uuid}-${index}-${Date.now()}`;
                if (!imgData.imageData) { // Check for empty placeholder
                  return {
                    id: newId, file: null, previewUrl: null, status: imgData.status || '',
                    isProcessing: false, needsProcessing: false, error: null,
                    angle: 0, isStatusFixed: !!templateToSelect, 
                  };
                }
                const file = dataURIToFile(imgData.imageData, imgData.fileName);
                const previewUrl = file ? URL.createObjectURL(file) : null;
                
                return {
                    id: newId,
                    file: file,
                    previewUrl: previewUrl,
                    status: imgData.status || '',
                    isProcessing: false,
                    needsProcessing: false, // Assume loaded data is processed
                    error: null,
                    angle: toClockwise(imgData.angle || 0),
                    isStatusFixed: !!templateToSelect, 
                };
            }));
            
            // If a template was selected, adjust the image array to match its settings
            if (templateToSelect) {
                const template = templates.find(p => p.name === templateToSelect);
                if (template) {
                    const finalImages = [...loadedImages];
                    const newCount = template.images_number;
                    const fixedStatuses = template.fixed_status;

                    if (finalImages.length > newCount) {
                        finalImages.splice(newCount);
                    } else if (finalImages.length < newCount) {
                         const generateId = () => typeof crypto !== 'undefined' && crypto.randomUUID ? crypto.randomUUID() : `new-${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
                         for (let i = finalImages.length; i < newCount; i++) {
                            finalImages.push({
                                id: generateId(), file: null, previewUrl: null, status: '',
                                isProcessing: false, needsProcessing: false, error: null, angle: 0, isStatusFixed: false
                            });
                         }
                    }

                    const imagesWithTemplateStatus = finalImages.map((img, i) => {
                        const hasFixedStatus = fixedStatuses.length > i && fixedStatuses[i] !== '';
                        return {
                            ...img,
                            status: hasFixedStatus ? fixedStatuses[i] : img.status,
                            isStatusFixed: hasFixedStatus
                        };
                    });
                     setImagesAndUpdateNumbers(imagesWithTemplateStatus);
                } else {
                     setImagesAndUpdateNumbers(loadedImages);
                }
            } else {
                 setImagesAndUpdateNumbers(loadedImages);
            }


            setCurrentRecordUuid(uuid); // Set the current record UUID
            setIsStagingSheetOpen(false); // Close the sheet
            toast({ title: t('successTitle'), description: t('loadRecordSuccess'), variant: 'default', className: "bg-accent text-accent-foreground"});

        } catch (error) {
            console.error("Error loading staged record:", error);
            const message = error instanceof Error ? error.message : String(error);
            toast({ title: t('errorTitle'), description: message, variant: 'destructive' });
        }
    }, [authToken, logout, t, toast, language, handleClearForm, setImagesAndUpdateNumbers, templates]);

    const openDeleteDialog = (record: StagedRecordSummary) => {
        setRecordToDelete(record);
        setIsDeleteDialogOpen(true);
    };

    const handleDeleteStagedRecord = async () => {
        if (!recordToDelete || !authToken) return;
        setIsDeleting(true);
        try {
            const response = await fetch(`${API_BASE_URL}/record/storage/${recordToDelete.uuid}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Accept-Language': language,
                },
            });

            if (response.status === 401) {
                toast({ title: t('sessionExpiredTitle'), description: t('sessionExpiredDescription'), variant: "destructive" });
                logout(); return;
            }

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || t('stageDeleteError'));
            }

            setStagedRecords(prev => prev.filter(r => r.uuid !== recordToDelete.uuid));

            toast({ title: t('successTitle'), description: t('deleteRecordSuccess'), variant: 'default', className: "bg-accent text-accent-foreground"});

        } catch (error) {
            const message = error instanceof Error ? error.message : t('stageDeleteError');
            toast({ title: t('errorTitle'), description: message, variant: "destructive" });
        } finally {
            setIsDeleting(false);
            setIsDeleteDialogOpen(false);
            setRecordToDelete(null);
        }
    };


  useEffect(() => {
    setIsClient(true);
    // initialization is now tied to fetchTemplates to avoid race conditions
  }, []);

  useEffect(() => {
    if (isClient && !authIsLoading && !isAuthenticated) {
      router.push('/login');
    }
    if (isClient && isAuthenticated && authToken) {
      fetchTemplates();
    }
  }, [isClient, authIsLoading, isAuthenticated, router, authToken, fetchTemplates]);


  useEffect(() => {
    async function fetchInitialDropdownOptions() {
        try {
            const staticOptions: DropdownOptions = { 
              company: ["Company Alpha", "Company Beta", "Company Gamma"],
              area: ["Area 1", "Area 2", "Area 3"],
              item: ["SN-00001", "SN-00002", "SN-00003", "SN-00004"],
              status: ["Operational", "Maintenance", "Offline", "Error"],
            };
            setDropdownOptions(staticOptions); 
        } catch (error) {
            console.error("Error fetching dropdown options:", error);
            toast({
              title: t('errorTitle'),
              description: t('optionsLoadErrorDescription'),
              variant: "destructive",
            });
            setDropdownOptions({ company: [], area: [], item: [], status: [] });
        } finally {
            setIsLoadingOptions(false);
        }
    }
    if (isClient) { 
        fetchInitialDropdownOptions();
    }
  }, [toast, t, isClient]); 

  useEffect(() => {
      if (isClient && dropdownOptions) {
          const finalOptions: DropdownOptions = {
              company: [], area: [], item: [], status: []
          };
          const keys: (keyof DropdownOptions)[] = ['company', 'area', 'item', 'status'];

          keys.forEach(key => {
              const history = getHistory(key, HISTORY_LIMIT);
              const defaultValues = dropdownOptions[key] || [];
              
              let combined = history.length > 0 ? history : defaultValues;
              
              const valueSet = new Set(combined);
              defaultValues.forEach(val => valueSet.add(val));
              
              finalOptions[key] = Array.from(valueSet);
          });
          setCombinedDropdownOptions(finalOptions);
      }
  }, [dropdownOptions, isClient]);

  const handleImageChange = useCallback(async (id: string, file: File | null) => {
    setImagesAndUpdateNumbers(prev =>
        prev.map(img => {
            if (img.id === id) {
                // If a file is being removed
                if (!file) {
                    if (img.previewUrl) URL.revokeObjectURL(img.previewUrl);
                    // For templates, keep the fixed status when the image is cleared
                    const statusToKeep = img.isStatusFixed ? img.status : '';
                    return {
                        ...img,
                        file: null,
                        previewUrl: null,
                        status: statusToKeep,
                        isProcessing: false,
                        needsProcessing: false,
                        error: null,
                        angle: 0,
                    };
                }
                
                // If a new file is being added
                const newPreviewUrl = URL.createObjectURL(file);
                if (img.previewUrl) URL.revokeObjectURL(img.previewUrl);
                
                // Keep the fixed status and state if part of a template
                const statusToKeep = img.isStatusFixed ? img.status : '';
                
                return {
                    ...img,
                    file: file,
                    previewUrl: newPreviewUrl,
                    isProcessing: false,
                    needsProcessing: true,
                    error: null,
                    angle: 0,
                    status: statusToKeep, // Explicitly keep the fixed status
                };
            }
            return img;
        })
    );
  }, []);

    const handleBatchProcessImages = useCallback(async () => {
        if (!authToken) {
            toast({ title: t('errorTitle'), description: t('sessionExpiredDescription'), variant: "destructive" });
            logout();
            return;
        }
        const imagesToProcess = images.filter(img => img.file);
        if (imagesToProcess.length === 0) {
            toast({ title: t('noImagesToProcessTitle'), description: t('noImagesToProcessDescription'), variant: "default" });
            return;
        }
        setIsBatchProcessing(true);
        setImagesAndUpdateNumbers(prev => prev.map(img =>
            imagesToProcess.some(p => p.id === img.id) ? { ...img, isProcessing: true, error: null, needsProcessing: false } : img
        ));

        try {
            const payloadImagesPromises = imagesToProcess.map(async (img) => ({
                id: img.id,
                imageData: await fileToBase64(img.file!),
                angle: toCounterClockwise(img.angle || 0),
            }));
            const payloadImages = await Promise.all(payloadImagesPromises);
            const payload: ProcessImagePayload = { images: payloadImages };

            let response: Response | null = null;
            const MAX_RETRIES = 3;

            for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
                try {
                    const res = await fetch(`${API_BASE_URL}/process-image`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${authToken}`,
                            'Accept-Language': language,
                        },
                        body: JSON.stringify(payload),
                    });
                    
                    if (res.ok || (res.status >= 400 && res.status < 500)) {
                        response = res;
                        break;
                    }

                    if (res.status >= 500) {
                        throw new Error(`Server error on attempt ${attempt}: ${res.status}`);
                    }
                } catch (error) {
                    console.error(`Attempt ${attempt} failed:`, error);
                    if (attempt < MAX_RETRIES) {
                        toast({
                            title: t('batchProcessingErrorTitle'),
                            description: t('processingRetryAttempt', { currentAttempt: attempt, maxAttempts: MAX_RETRIES }),
                            variant: 'default'
                        });
                        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
                    } else {
                        throw new Error(t('processingFailedAfterRetries'));
                    }
                }
            }
            
            if (!response) {
                throw new Error(t('processingFailedAfterRetries'));
            }

            if (response.status === 401) {
                toast({ title: t('sessionExpiredTitle'), description: t('sessionExpiredDescription'), variant: "destructive" });
                logout();
                setIsBatchProcessing(false);
                return;
            }

            const responseData: ProcessImageResponse = await response.json();
            if (responseData?.error && !responseData?.results) throw new Error(responseData.error);
            if (!response.ok && !responseData?.results) throw new Error(responseData?.error || `HTTP error! status: ${response.status}`);
            if (!responseData?.results || !Array.isArray(responseData.results)) throw new Error(t('invalidApiResponse'));

            if (responseData.company) {
              if (responseData.company === IDENTIFY_CONFLICTS_VALUE) {
                setCompanyConflict(true);
              } else {
                setCompany(responseData.company);
                setCompanyConflict(false);
              }
            } else { setCompanyConflict(false); }

            if (responseData.item) {
              if (responseData.item === IDENTIFY_CONFLICTS_VALUE) {
                setItemConflict(true);
              } else {
                setItem(responseData.item);
                setItemConflict(false);
              }
            } else { setItemConflict(false); }
            
            if (responseData.area) {
                setArea(responseData.area);
            }

            setImagesAndUpdateNumbers(prev => prev.map(img => {
                const result = responseData?.results?.find((r: ImageResult) => r.id === img.id);
                if (!imagesToProcess.some(p => p.id === img.id)) return img;
                if (!result) return { ...img, isProcessing: false, needsProcessing: false, error: t('apiNoResultError'), status: '' };
                
                if (result.success) {
                    return { 
                        ...img, 
                        // If status is fixed by a template, don't update it from the API response
                        status: img.isStatusFixed ? img.status : (result.status ?? ''), 
                        isProcessing: false, 
                        needsProcessing: false, 
                        error: null, 
                        angle: toClockwise(result.angle ?? 0) 
                    };
                }

                return { ...img, isProcessing: false, needsProcessing: true, error: result.error || t('unknownProcessingError'), status: '' };
            }));
            toast({ title: t('processingCompleteTitle'), description: t('processingCompleteDescription'), variant: "default", className: "bg-accent text-accent-foreground" });
        } catch (error) {
            console.error('Error processing images:', error);
            const errorMessage = error instanceof Error ? error.message : t('unknownBatchError');
            toast({ title: t('batchProcessingErrorTitle'), description: errorMessage, variant: "destructive" });
            setImagesAndUpdateNumbers(prev => prev.map(img =>
                imagesToProcess.some(p => p.id === img.id) ? { ...img, isProcessing: false, needsProcessing: true, error: errorMessage, status: '' } : img
            ));
        } finally {
            setIsBatchProcessing(false);
        }
    }, [images, toast, t, authToken, logout, language]);


  const handleImageDataChange = (id: string, field: keyof Pick<SingleImageData, 'status' | 'angle'>, value: string | number) => {
     setImagesAndUpdateNumbers(prev =>
         prev.map(img => img.id === id ? { ...img, [field]: value } : img)
     );
  };

 const handleRemoveImage = (id: string, event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
     event?.preventDefault();
     event?.stopPropagation();
     const imgToRemove = images.find(img => img.id === id);
     if (!imgToRemove) return;
     
     if (selectedTemplateName !== '') {
        handleImageChange(id, null);
     } else {
        const isFilePresent = !!imgToRemove.file;
        setImagesAndUpdateNumbers(prev => {
            if (isFilePresent) {
                return prev.map(img => {
                    if (img.id === id) {
                        if (img.previewUrl) URL.revokeObjectURL(img.previewUrl);
                        return {
                            ...img,
                            file: null,
                            previewUrl: null,
                            status: '',
                            isProcessing: false,
                            needsProcessing: false,
                            error: null,
                            angle: 0,
                            isStatusFixed: false,
                        };
                    }
                    return img;
                });
            } else if (prev.length > MIN_IMAGES) {
                return prev.filter(img => img.id !== id);
            } else {
                toast({
                    title: t('cannotRemoveLastSlotTitle'),
                    description: t('cannotRemoveLastSlotDescription', { minImages: MIN_IMAGES }),
                    variant: "default"
                });
                return prev;
            }
        });
     }
  };


  const handleAddMoreImages = () => {
    if (selectedTemplateName) {
        toast({ title: t('errorTitle'), description: "Cannot add images manually when a template is selected.", variant: "destructive" });
        return;
    }
    setImagesAndUpdateNumbers(prev => {
      if (prev.length >= MAX_IMAGES) {
        toast({ title: t('maxImages', { maxImages: MAX_IMAGES }), variant: "default" });
        return prev;
      }
      const generateId = () => typeof crypto !== 'undefined' && crypto.randomUUID ? crypto.randomUUID() : `new-${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
      const newImage: SingleImageData = { id: generateId(), file: null, previewUrl: null, status: '', isProcessing: false, needsProcessing: false, error: null, angle: 0, isStatusFixed: false };
      return [...prev, newImage];
    });
  };

  const getPayload = async (isStaging = false): Promise<ExcelPayload | null> => {
    try {
        const imagesToSubmitPromises = images.map(async (img) => {
            if (img.file) {
                const imageData = await fileToBase64(img.file);
                return { 
                    fileName: img.file.name, 
                    status: img.status, 
                    imageData,
                    angle: toCounterClockwise(img.angle || 0),
                };
            }
            // For staging, or for template mode submission, we need placeholders for empty slots.
            if (isStaging || selectedTemplateName !== '') {
                 return {
                    fileName: '',
                    imageData: '',
                    status: img.status, // Still include the fixed status
                    angle: 0,
                 };
            }
            return null;
        });
        
        let allImagesData = await Promise.all(imagesToSubmitPromises);
        
        // When not staging and not in template mode, filter out empty slots for final submission.
        if (!isStaging && selectedTemplateName === '') {
            allImagesData = allImagesData.filter(img => img !== null);
        }

        if (allImagesData.length === 0 && !isStaging) {
            toast({ title: t('missingValidImagesTitle'), description: "Please upload at least one image before submitting.", variant: "destructive" });
            return null;
        }

        const template = templates.find(p => p.name === selectedTemplateName)?.template_name;

        const payload: ExcelPayload = {
            company,
            item,
            area,
            images: allImagesData as any, // Cast because we've handled nulls/placeholders as needed
            template: template,
            ...(currentRecordUuid && { record_uuid: currentRecordUuid }),
        };
        return payload;
    } catch (error) {
        console.error("Error creating payload:", error);
        toast({ title: t('errorTitle'), description: "Failed to prepare data for submission.", variant: "destructive" });
        return null;
    }
  };


  const handleStageData = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!authToken) {
        toast({ title: t('errorTitle'), description: t('sessionExpiredDescription'), variant: "destructive" });
        logout(); return;
    }
    if (!isFormReadyForStaging) {
      toast({ title: t('missingInfoTitle'), description: "Company, Item, Area and at least one image are required to stage data.", variant: "destructive" });
      return;
    }
    setIsStaging(true);

    const payload = await getPayload(true); // Pass true for isStaging
    if (!payload) {
        setIsStaging(false);
        return;
    }
    
    // Remove templates from staging payload
    delete payload.template;
    
    try {
        const response = await fetch(`${API_BASE_URL}/record/storage`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${authToken}`,
              'Accept-Language': language,
            },
            body: JSON.stringify(payload),
        });
        
        if (response.status === 401) {
            toast({ title: t('sessionExpiredTitle'), description: t('sessionExpiredDescription'), variant: "destructive" });
            logout(); return;
        }

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || t('stageError'));
        }
        
        toast({ title: t('stageSuccessTitle'), description: t('stageSuccessDescription'), variant: 'default', className: "bg-accent text-accent-foreground"});
        handleClearForm(false); // Clear form after successful staging

    } catch (error) {
        const message = error instanceof Error ? error.message : t('stageError');
        toast({ title: t('stageErrorTitle'), description: message, variant: "destructive" });
    } finally {
        setIsStaging(false);
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!authToken) {
        toast({ title: t('errorTitle'), description: t('sessionExpiredDescription'), variant: "destructive" });
        logout();
        return;
    }
    if (!isSubmitButtonActive) {
      // Re-validate and show specific toast
      if (!company || !item || !area) {
        toast({ title: t('missingInfoTitle'), description: t('missingInfoDescription'), variant: 'destructive' });
      } else if (!selectedTemplateName) {
        toast({ title: t('errorTitle'), description: "Please select a template before submitting.", variant: 'destructive' });
      } else if (images.filter(img => img.file).some(img => !img.status)) {
        toast({ title: t('missingStatusTitle'), description: t('missingStatusDescription'), variant: 'destructive' });
      }
      return;
    }
    setIsSubmitting(true);
    setGeneratedFiles([]); // Clear previous results before a new submission

    if (isClient) {
        if (company && company !== IDENTIFY_CONFLICTS_VALUE) saveHistory('company', company, HISTORY_LIMIT);
        if (area && area !== IDENTIFY_CONFLICTS_VALUE) saveHistory('area', area, HISTORY_LIMIT);
        if (item && item !== IDENTIFY_CONFLICTS_VALUE) saveHistory('item', item, HISTORY_LIMIT);
        
        images.filter(img => img.file).forEach(img => { if (img.status) saveHistory('status', img.status, HISTORY_LIMIT); });
         
        if (dropdownOptions) { 
             const keys: (keyof DropdownOptions)[] = ['company', 'area', 'item', 'status'];
             const updatedOptions: DropdownOptions = { company: [], area: [], item: [], status: [] };
             keys.forEach(key => {
                const history = getHistory(key, HISTORY_LIMIT);
                const defaultValues = dropdownOptions[key] || [];
                const uniqueValues = new Set([...history, ...defaultValues]);
                updatedOptions[key] = Array.from(uniqueValues);
             });
             setCombinedDropdownOptions(updatedOptions);
         }
    }
    
    const payload = await getPayload(false); // Pass false for submission
    if (!payload) {
        setIsSubmitting(false);
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/generate-excel`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${authToken}`,
              'Accept-Language': language,
            },
            body: JSON.stringify(payload),
        });

        if (response.status === 401) {
            toast({ title: t('sessionExpiredTitle'), description: t('sessionExpiredDescription'), variant: "destructive" });
            logout();
            setIsSubmitting(false);
            return;
        }
        
        const responseData: ExcelResponse = await response.json();

        if (!response.ok) { 
            throw new Error(responseData.message || `HTTP error! status: ${response.status}`);
        }
        
        if (!responseData.files || responseData.files.length === 0) {
             throw new Error(t('invalidExcelApiResponse'));
        }
        
        setGeneratedFiles(responseData.files);
        setIsSubmissionSuccessful(true);
        setIsDownloadDialogOpen(true);
        setCurrentRecordUuid(null); // Clear UUID after successful submission

    } catch (error) {
        console.error('Error submitting data:', error);
        const errorMessage = error instanceof Error ? error.message : t('submitError');
        toast({ title: t('submitError'), description: errorMessage, variant: "destructive" });
    } finally {
        setIsSubmitting(false);
    }
  };

  const handleLanguageChange = (lang: Language) => setLanguage(lang);
  
  const imageIds = useMemo(() => images.map(img => img.id), [images]);
  const hasImagesToProcess = useMemo(() => images.some(img => img.file && !img.isProcessing), [images]);

  const isFormReadyForStaging = useMemo(() => {
    return !!company && !!item && !!area && images.some(img => img.file);
  }, [company, item, area, images]);

  const isFormReadyForSubmit = useMemo(() => {
    // A template must be selected.
    if (!selectedTemplateName) return false;

    const headerInfoComplete = !!company && !!item && !!area &&
                               !companyConflict && !itemConflict;

    const uploadedImages = images.filter(img => img.file);
    if (uploadedImages.length === 0 && selectedTemplateName === '') {
        return false;
    }
    const allImagesHaveStatus = images.every(img => {
        // If there's a file, it must have a status. If no file, it's ok.
        return img.file ? (img.status && img.status.trim() !== '') : true;
    });

    return headerInfoComplete && allImagesHaveStatus;
  }, [company, item, area, images, companyConflict, itemConflict, selectedTemplateName]);

  const isProcessImagesButtonActive = useMemo(() => {
    return images.some(img => img.file) && !isBatchProcessing && !isSubmitting;
  }, [images, isBatchProcessing, isSubmitting]);

  const isSubmitButtonActive = useMemo(() => {
    return isFormReadyForSubmit && !isSubmitting && !isBatchProcessing;
  }, [isFormReadyForSubmit, isSubmitting, isBatchProcessing]);


  if (!isClient || authIsLoading || (isClient && !authIsLoading && !isAuthenticated)) {
    return <DataEntryFormSkeleton t={t} isTranslationEnabled={isTranslationEnabled} />;
  }

  return (
    <TooltipProvider>
    <div className="flex flex-col h-screen">
       <AppHeader title={t('appName')} />

       <div className="flex-1 overflow-y-auto p-4 md:p-8">
          <form onSubmit={handleSubmit} className="space-y-8">
            <Card className="shadow-sm">
                <CardHeader>
                    <CardTitle className="text-lg">{t('headerInfo')}</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {isLoadingOptions || !combinedDropdownOptions ? (
                        <> <SkeletonField /> <SkeletonField /> <SkeletonField /> </>
                    ) : combinedDropdownOptions ? (
                        <>
                            <div className="space-y-1">
                                <div className="flex items-center">
                                    <Label htmlFor="item">{t('item')}</Label>
                                    {itemConflict && (
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <AlertCircle className="h-4 w-4 text-destructive inline-block ml-1 cursor-help" />
                                            </TooltipTrigger>
                                            <TooltipContent><p>{t('identifyConflictMessage')}</p></TooltipContent>
                                        </Tooltip>
                                    )}
                                </div>
                                <ComboboxInput options={combinedDropdownOptions.item} value={item} 
                                    onChange={(newValue) => { setItem(newValue); setItemConflict(false); }} 
                                    placeholder={t('selectOrEnter')} inputPlaceholder={t('searchOrEnter')} 
                                    disabled={isBatchProcessing || isSubmitting} aria-label={t('item')} />
                            </div>
                            <div className="space-y-1">
                                <div className="flex items-center">
                                    <Label htmlFor="company">{t('company')}</Label>
                                    {companyConflict && (
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <AlertCircle className="h-4 w-4 text-destructive inline-block ml-1 cursor-help" />
                                            </TooltipTrigger>
                                            <TooltipContent><p>{t('identifyConflictMessage')}</p></TooltipContent>
                                        </Tooltip>
                                    )}
                                </div>
                                <ComboboxInput options={combinedDropdownOptions.company} value={company} 
                                    onChange={(newValue) => { setCompany(newValue); setCompanyConflict(false); }} 
                                    placeholder={t('selectOrEnter')} inputPlaceholder={t('searchOrEnter')} 
                                    disabled={isBatchProcessing || isSubmitting} aria-label={t('company')} />
                            </div>
                            <div className="space-y-1">
                                <div className="flex items-center">
                                    <Label htmlFor="area">{t('area')}</Label>
                                </div>
                                <ComboboxInput options={combinedDropdownOptions.area} value={area} 
                                    onChange={setArea} 
                                    placeholder={t('selectOrEnter')} inputPlaceholder={t('searchOrEnter')} 
                                    disabled={isBatchProcessing || isSubmitting} aria-label={t('area')} />
                            </div>
                        </>
                    ) : (
                        <p className="text-destructive col-span-full">{t('optionsLoadError')}</p>
                    )}
                </CardContent>
            </Card>

            
            <Card className="shadow-sm flex-grow">
                <CardHeader className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 pb-2">
                     <div className='flex items-center gap-2'>
                        <CardTitle className="text-lg">{t('images')}</CardTitle>
                        {isMobile && (
                            <ToggleGroup type="single" value={uploadMode} onValueChange={(value) => {if (value) setUploadMode(value as 'album' | 'camera')}} className="gap-0.5">
                                <ToggleGroupItem value="album" aria-label="Select from album" className="px-2.5">
                                    <ImagesIcon className="h-5 w-5" />
                                </ToggleGroupItem>
                                <ToggleGroupItem value="camera" aria-label="Use camera" className="px-2.5">
                                    <Camera className="h-5 w-5" />
                                </ToggleGroupItem>
                            </ToggleGroup>
                         )}
                     </div>
                    <div className='w-full md:w-auto'>
                        {isLoadingTemplates ? (
                            <Skeleton className="h-10 w-full md:w-[180px]" />
                        ) : (
                            <Select onValueChange={handleTemplateChange} value={selectedTemplateName} disabled={isSubmitting || isBatchProcessing || templates.length === 0}>
                              <SelectTrigger className="w-full md:w-[180px]">
                                <SelectValue placeholder={t('templateSelectPlaceholder')} />
                              </SelectTrigger>
                              <SelectContent>
                                {templates.map(option => (
                                    <SelectItem key={option.name} value={option.name}>{option.name}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                        )}
                    </div>
                </CardHeader>
                <CardContent className="space-y-6 pt-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4">
                        {images.map((img) => (
                            <ImageUploadCard
                                key={img.id}
                                imageData={img}
                                options={combinedDropdownOptions ? { status: combinedDropdownOptions.status } : { status: [] }}
                                onImageChange={handleImageChange}
                                onDataChange={handleImageDataChange}
                                onRemove={handleRemoveImage}
                                uploadMode={uploadMode}
                                className="h-full"
                            />
                        ))}
                        {!selectedTemplateName && images.length < MAX_IMAGES && (
                            <Card 
                                className="w-full h-full border-dashed border-2 hover:border-primary transition-colors flex items-center justify-center cursor-pointer bg-muted/50 hover:bg-muted min-h-[300px]" 
                                onClick={handleAddMoreImages}
                            >
                                <div className="text-center text-muted-foreground group-hover:text-primary">
                                    <Plus className="h-10 w-10 mx-auto mb-2" />
                                    <span>{t('addMore')}</span>
                                </div>
                            </Card>
                        )}
                    </div>
                    {!selectedTemplateName && images.length >= MAX_IMAGES && (<p className="text-sm text-muted-foreground text-center">{t('maxImages', { maxImages: MAX_IMAGES })}</p>)}
                </CardContent>
            </Card>

            <div className="flex flex-col md:flex-row md:justify-end gap-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => handleClearForm()}
                        disabled={isSubmitting || isBatchProcessing || isStaging}
                        size="lg"
                    >
                        <RotateCcw className="mr-2 h-4 w-4" /> {t('clearFormButton')}
                    </Button>
                    <Button
                        type="button"
                        variant={isFormReadyForStaging ? "default" : "outline"}
                        onClick={handleStageData}
                        disabled={!isFormReadyForStaging || isStaging || isSubmitting}
                        className={cn(isFormReadyForStaging && "bg-green-600 hover:bg-green-700")}
                        size="lg"
                    >
                        {isStaging ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Archive className="mr-2 h-4 w-4" />}
                        {isStaging ? t('stagingData') : t('stageButton')}
                    </Button>
                    <Button
                        type="button"
                        onClick={handleBatchProcessImages}
                        variant={!isProcessImagesButtonActive ? "secondary" : "default"}
                        className={cn(
                        "w-full",
                        isProcessImagesButtonActive && "bg-accent hover:bg-accent/90 text-accent-foreground"
                        )}
                        disabled={!isProcessImagesButtonActive}
                        size="lg"
                    >
                        {isBatchProcessing ? ( <> <Loader2 className="mr-2 h-4 w-4 animate-spin" /> {t('processingImages')} </> ) : ( <> <Images className="mr-2 h-4 w-4" /> {t('processImages')} </> )}
                    </Button>
                    {isSubmissionSuccessful && generatedFiles.length > 0 ? (
                        <Button
                            type="button"
                            onClick={() => setIsDownloadDialogOpen(true)}
                            size="lg"
                            className="w-full bg-green-600 hover:bg-green-700 text-white"
                        >
                            <Download className="mr-2 h-4 w-4" /> {t('viewDownloadLinks')}
                        </Button>
                    ) : (
                        <Button
                            type="submit"
                            variant={!isSubmitButtonActive ? "secondary" : "default"}
                            disabled={!isSubmitButtonActive}
                            size="lg"
                            className={cn(
                                "w-full",
                                isSubmitButtonActive && "bg-accent hover:bg-accent/90 text-accent-foreground"
                            )}
                        >
                            {isSubmitting ? ( 
                                <> <Loader2 className="mr-2 h-4 w-4 animate-spin" /> {t('submitting')} </> 
                            ) : ( 
                                <> <Download className="mr-2 h-4 w-4" /> {t('submit')} </> 
                            )}
                        </Button>
                    )}
                </div>
            </div>
          </form>
        </div>

      <Dialog open={isDownloadDialogOpen} onOpenChange={(isOpen) => {
          setIsDownloadDialogOpen(isOpen);
          if (!isOpen && isSubmissionSuccessful) {
              // Don't reset isSubmissionSuccessful here, so the "View Downloads" button remains.
              // It will be reset by the form edit useEffect.
          }
      }}>
        <DialogContent>
            <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                {t('generationSuccessTitle')}
            </DialogTitle>
            <DialogDescription>{t('downloadFilesDescription')}</DialogDescription>
            </DialogHeader>
            <div className="py-4 space-y-4">
              <ul className="space-y-3">
                  {generatedFiles.map((file, index) => (
                  <li key={index} className="flex items-center justify-between p-3 rounded-lg border bg-muted/50">
                      <span className="font-medium truncate" title={decodeURIComponent(file.filename)}>{decodeURIComponent(file.filename)}</span>
                      <Button asChild size="sm">
                      <a href={file.url} download={decodeURIComponent(file.filename)} target="_blank" rel="noopener noreferrer">
                          <Download className="mr-2 h-4 w-4" />
                          {t('downloadButton')}
                      </a>
                      </Button>
                  </li>
                  ))}
              </ul>
              <p className="text-xs text-muted-foreground text-center">{t('downloadLinkExpiration')}</p>
            </div>
            <DialogFooter>
                <DialogClose asChild>
                    <Button type="button" variant="secondary">{t('closeButton')}</Button>
                </DialogClose>
            </DialogFooter>
        </DialogContent>
      </Dialog>
      
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
            <DialogHeader>
                <DialogTitle>{t('deleteStagedRecordTitle')}</DialogTitle>
                <DialogDescription>
                    {t('deleteStagedRecordMessage', { recordName: recordToDelete?.record_name || '' })}
                </DialogDescription>
            </DialogHeader>
            <DialogFooter>
                <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)} disabled={isDeleting}>
                    {t('adminCancel')}
                </Button>
                <Button variant="destructive" onClick={handleDeleteStagedRecord} disabled={isDeleting}>
                    {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {t('confirmDelete')}
                </Button>
            </DialogFooter>
        </DialogContent>
      </Dialog>

      <Toaster />
    </div>
    </TooltipProvider>
  );
}

const SkeletonField = () => ( <div className="space-y-2"> <Skeleton className="h-4 w-1/4" /> <Skeleton className="h-10 w-full" /> </div> );

export default function Home() {
  return <CVRecordPage />;
}

const DataEntryFormSkeleton = ({ t, isTranslationEnabled }: { t: Function, isTranslationEnabled: boolean }) => {
     return (
        <div className="flex flex-col h-screen animate-pulse">
            <AppHeader title={t('appName')} />
            <div className="flex-1 overflow-y-auto p-4 md:p-8 space-y-8">
                <Card className="shadow-sm">
                    <CardHeader> <Skeleton className="h-6 w-1/2" /> </CardHeader>
                    <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <SkeletonField /> <SkeletonField /> <SkeletonField /> 
                    </CardContent>
                </Card>
                 <Card className="shadow-sm">
                     <CardHeader className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 pb-2">
                          <Skeleton className="h-6 w-1/5" />
                          <Skeleton className="h-10 w-full md:w-[180px]" />
                      </CardHeader>
                     <CardContent className="space-y-6 pt-4">
                        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4">
                             {Array.from({ length: DEFAULT_IMAGES }).map((_, i) => (
                                <Card key={`skel-${i}`} className="h-full">
                                    <CardHeader className="p-4 border-b">
                                        <div className="aspect-video relative bg-muted rounded-md" />
                                    </CardHeader>
                                    <CardContent className="p-4 space-y-4">
                                        <Skeleton className="h-10 w-full" />
                                    </CardContent>
                                </Card>
                            ))}
                         </div>
                     </CardContent>
                 </Card>
                 
                <div className="flex flex-col md:flex-row md:justify-end gap-6">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <Skeleton className="h-12 w-full rounded-md" />
                        <Skeleton className="h-12 w-full rounded-md" />
                        <Skeleton className="h-12 w-full rounded-md" />
                        <Skeleton className="h-12 w-full rounded-md" />
                    </div>
                </div>
            </div>
        </div>
     );
};
