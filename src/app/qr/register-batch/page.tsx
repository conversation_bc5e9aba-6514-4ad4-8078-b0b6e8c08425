
'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Toaster } from "@/components/ui/toaster";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/contexts/auth-context';
import { useRouter } from 'next/navigation';
import { useTranslations } from '@/hooks/use-translations';
import { Loader2, Upload, Download, FileSpreadsheet, CheckCircle, AlertCircle, List } from 'lucide-react';
import { AppHeader } from '@/components/app-header';
import type { QrRegisterResponse } from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";

interface BatchResponse {
  url: string;
  batch_id: string;
  total_records: number;
  serial_numbers: string[];
  error?: string;
}

export default function QrBatchRegisterPage() {
  const { t } = useTranslations();
  const { toast } = useToast();
  const { token: authToken, isLoading: authIsLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  // Component State
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<BatchResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (isClient && !authIsLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isClient, authIsLoading, isAuthenticated, router]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const allowedTypes = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      if (allowedTypes.includes(file.type) || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        setSelectedFile(file);
        setError(null);
      } else {
        toast({
          title: t('errorTitle'),
          description: t('qrBatchInvalidFileType'),
          variant: 'destructive',
        });
        setSelectedFile(null);
      }
    }
  };

  const handleSubmit = async () => {
    if (!selectedFile || !authToken) {
      toast({
        title: t('errorTitle'),
        description: t('qrBatchNoFileSelected'),
        variant: 'destructive',
      });
      return;
    }

    setIsUploading(true);
    setError(null);
    setUploadResult(null);

    const formData = new FormData();
    formData.append('file', selectedFile);

    try {
      const response = await fetch(`${API_BASE_URL}/qr/register_batch`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
        body: formData,
      });

      const data: BatchResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'An unknown error occurred during batch registration.');
      }

      setUploadResult(data);
      toast({
        title: t('successTitle'),
        description: t('qrBatchSuccessMessage', { count: data.total_records }),
        variant: 'default',
        className: 'bg-accent text-accent-foreground',
      });

    } catch (err) {
      const message = err instanceof Error ? err.message : String(err);
      setError(message);
      toast({
        title: t('errorTitle'),
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };
  
  const handleReset = () => {
      setSelectedFile(null);
      setUploadResult(null);
      setError(null);
  };


  if (authIsLoading || !isClient) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-col h-screen" id="qr-batch-register-page">
        <AppHeader title={t('qrBatchRegisterTitle')} />
        <div className="flex-1 flex items-center justify-center bg-muted/20 p-4">
          <Card className="w-full max-w-2xl shadow-xl rounded-lg">
            <CardHeader>
              <CardTitle>{t('qrBatchRegisterTitle')}</CardTitle>
              <CardDescription>
                {t('qrBatchRegisterDescription')}
                <br />
                <span className="text-sm text-muted-foreground mt-2 block">{t('qrSameItemWarning')}</span>
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              {uploadResult ? (
                <div className="text-center p-6 bg-green-50 rounded-lg border border-green-200">
                    <CheckCircle className="h-16 w-16 mx-auto text-green-600 mb-4" />
                    <h3 className="text-xl font-semibold text-green-800">{t('qrBatchCompleteTitle')}</h3>
                    <p className="text-muted-foreground mt-2 mb-4">{t('qrBatchSuccessMessage', { count: uploadResult.total_records })}</p>
                    <div className="space-y-4">
                        <Button asChild size="lg" className="w-full">
                            <a href={uploadResult.url} download="qrcodes.zip">
                                <Download className="mr-2 h-5 w-5" />
                                {t('qrBatchDownloadZip')}
                            </a>
                        </Button>
                        <Button variant="outline" onClick={handleReset} className="w-full">
                            {t('registerAnother')}
                        </Button>
                    </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div
                    className="relative flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-lg bg-muted/50 hover:bg-muted cursor-pointer"
                    onClick={() => document.getElementById('batch-file-input')?.click()}
                  >
                    <input
                      id="batch-file-input"
                      type="file"
                      accept=".xlsx, .xls, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                      onChange={handleFileChange}
                      className="hidden"
                      disabled={isUploading}
                    />
                    {selectedFile ? (
                      <div className="text-center text-primary">
                        <FileSpreadsheet className="h-12 w-12 mx-auto mb-2" />
                        <p className="font-semibold">{selectedFile.name}</p>
                        <p className="text-xs text-muted-foreground">{t('qrBatchFileSelected')}</p>
                      </div>
                    ) : (
                      <div className="text-center text-muted-foreground">
                        <Upload className="h-12 w-12 mx-auto mb-2" />
                        <p className="font-semibold">{t('qrBatchUploadPrompt')}</p>
                      </div>
                    )}
                  </div>

                  {error && (
                    <div className="text-sm text-destructive flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        {error}
                    </div>
                  )}

                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button
                      onClick={handleSubmit}
                      disabled={!selectedFile || isUploading}
                      className="w-full"
                      size="lg"
                    >
                      {isUploading && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
                      {isUploading ? t('qrBatchUploading') : t('qrBatchSubmitButton')}
                    </Button>
                    <Button asChild variant="secondary" className="w-full" size="lg">
                      <a href="https://kr-oss.990313.xyz/checklist/res/batch_register.xlsx?OSSAccessKeyId=LTAI5tB3ZexFHLHGWKBLrT16&Expires=2115849467&Signature=utwp4zdj8su0StXsXm%2FTLXluXN8%3D" target="_blank" rel="noopener noreferrer">
                        <Download className="mr-2 h-5 w-5" />
                        {t('qrBatchDownloadTemplate')}
                      </a>
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      <Toaster />
    </>
  );
}
