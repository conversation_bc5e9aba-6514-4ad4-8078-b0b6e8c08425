
'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Toaster } from "@/components/ui/toaster";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/contexts/auth-context';
import { useTranslations } from '@/hooks/use-translations';
import { Loader2, Upload, QrCode, CheckCircle, AlertCircle, Eye, X, Camera, Edit3, Save, XCircle } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, AlertTitle } from '@/components/ui/alert';
import type { QrRecordDataResponse, QrRecordImageUploadResponse } from '@/types';
import { AppHeader } from '@/components/app-header';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";

// Declare OpencvQr for TypeScript since it's loaded from a script tag
declare var OpencvQr: any;

export default function QrRecordPage() {
  const { t } = useTranslations();
  const { toast } = useToast();
  const { token: authToken, isLoading: authIsLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  // Component State
  const [cvQr, setCvQr] = useState<any>(null);
  const [cvQrInitError, setCvQrInitError] = useState<string | null>(null);
  const [cvQrRetryCount, setCvQrRetryCount] = useState(0);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [scannedSn, setScannedSn] = useState<string | null>(null);
  const [recordData, setRecordData] = useState<QrRecordDataResponse | null>(null);
  
  const [isFetchingRecord, setIsFetchingRecord] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);
  const [isImageViewerOpen, setIsImageViewerOpen] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);

  // Nameplate State
  const [isNameplateDialogOpen, setIsNameplateDialogOpen] = useState(false);
  const [nameplateData, setNameplateData] = useState<string[][] | null>(null);
  const [editableNameplateData, setEditableNameplateData] = useState<string[][] | null>(null);
  const [isFetchingNameplate, setIsFetchingNameplate] = useState(false);
  const [isUpdatingNameplate, setIsUpdatingNameplate] = useState(false);
  const [nameplateError, setNameplateError] = useState<string | null>(null);

  // Camera Scanning State
  const [isLiveCapturing, setIsLiveCapturing] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [frameCount, setFrameCount] = useState(0);
  const videoRef = useRef<HTMLVideoElement>(null);
  const cameraCanvasRef = useRef<HTMLCanvasElement>(null);
  const captureCanvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameId = useRef<number>();
  const currentStreamRef = useRef<MediaStream | null>(null);

  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Authentication check
  useEffect(() => {
    if (!authIsLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [authIsLoading, isAuthenticated, router]);

  const resetState = useCallback((fullReset = true) => {
    if (capturedImage) URL.revokeObjectURL(capturedImage);
    setImageFile(null);
    setCapturedImage(null);
    if (fullReset) {
      setScannedSn(null);
      setRecordData(null);
      setFetchError(null);
    }
  }, [capturedImage]);

  const initializeOpencvQr = useCallback(async (retryCount = 0) => {
    const maxRetries = 3;
    const retryDelay = 2000; // 2 seconds

    try {
      setCvQrInitError(null);

      if (typeof OpencvQr !== 'undefined') {
        console.log(`Initializing OpenCV QR (attempt ${retryCount + 1}/${maxRetries + 1})`);

        const qrInstance = new OpencvQr({
          dw: "https://leidenglai.github.io/opencv-js-qrcode/models/detect.caffemodel",
          sw: "https://leidenglai.github.io/opencv-js-qrcode/models/sr.caffemodel",
        });

        // Wait a bit to ensure models are loaded
        await new Promise(resolve => setTimeout(resolve, 1000));

        setCvQr(qrInstance);
        setCvQrRetryCount(0);
        console.log('OpenCV QR initialized successfully');
      } else {
        throw new Error('OpencvQr is not available');
      }
    } catch (error) {
      console.error('OpenCV QR initialization failed:', error);
      setCvQrInitError(error instanceof Error ? error.message : 'Initialization failed');

      if (retryCount < maxRetries) {
        console.log(`Retrying OpenCV QR initialization in ${retryDelay}ms...`);
        setCvQrRetryCount(retryCount + 1);
        setTimeout(() => {
          initializeOpencvQr(retryCount + 1);
        }, retryDelay);
      } else {
        console.error('OpenCV QR initialization failed after all retries');
        toast({
          title: t('errorTitle'),
          description: t('qrScannerInitError'),
          variant: 'destructive',
        });
      }
    }
  }, [toast, t]);

  // Initialize OpenCV QR on client side
  useEffect(() => {
    setIsClient(true);
    // Initialize OpenCV QR with retry mechanism
    initializeOpencvQr();
  }, [initializeOpencvQr]);

  const stopCamera = useCallback(() => {
    console.log('stopCamera called');

    // Cancel animation frame
    if (animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
      animationFrameId.current = undefined;
    }

    // Stop current stream if exists
    if (currentStreamRef.current) {
      currentStreamRef.current.getTracks().forEach(track => {
        if (track.readyState !== 'ended') {
          track.stop();
          console.log(`Stopped ${track.kind} track:`, track.label, 'state:', track.readyState);
        }
      });
      currentStreamRef.current = null;
    }

    // Stop all media tracks and clear video source
    if (videoRef.current) {
      const video = videoRef.current;

      if (video.srcObject) {
        const stream = video.srcObject as MediaStream;

        // Stop all tracks (video and audio if any)
        stream.getTracks().forEach(track => {
          if (track.readyState !== 'ended') {
            track.stop();
            console.log(`Stopped ${track.kind} track from video:`, track.label, 'state:', track.readyState);
          }
        });

        // Clear the video source immediately
        video.srcObject = null;
      }

      // Pause and reset video element
      video.pause();
      video.currentTime = 0;
      video.load(); // Force video element to release resources

      // Additional cleanup - remove all event listeners
      video.onloadedmetadata = null;
      video.oncanplay = null;
    }

    // Force garbage collection hint (not guaranteed but helps)
    if (window.gc) {
      window.gc();
    }

    console.log('stopCamera completed');
  }, []);

  const loadImageToCanvas = (url: string, canvas: HTMLCanvasElement, callback: () => void) => {
    const ctx = canvas.getContext("2d", { willReadFrequently: true });
    const img = new window.Image();
    img.crossOrigin = "anonymous";
    img.onload = function () {
      const { width, height } = img;
      canvas.width = width;
      canvas.height = height;
      ctx?.drawImage(img, 0, 0, width, height);
      callback();
    };
    img.src = url;
  };
  
  const scanWithSlidingWindow = (sourceCanvas: HTMLCanvasElement): Promise<string | null> => {
      return new Promise((resolve) => {
        if (!cvQr) return resolve(null);
        
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d', { willReadFrequently: true });
        if (!tempCtx) return resolve(null);
        
        // AI绝对不要改我这两个参数，不然骂死你
        const windowSize = 1000;
        const stepSize = 500;

        tempCanvas.width = windowSize;
        tempCanvas.height = windowSize;
        
        const totalStepsX = sourceCanvas.width > windowSize ? Math.floor((sourceCanvas.width - windowSize) / stepSize) + 1 : 1;
        const totalStepsY = sourceCanvas.height > windowSize ? Math.floor((sourceCanvas.height - windowSize) / stepSize) + 1 : 1;
        const totalSteps = totalStepsX * totalStepsY;
        let currentStep = 0;

        let y = 0;
        const scanY = () => {
            if (y > sourceCanvas.height - windowSize) {
                setScanningProgress(null);
                return resolve(null); // Finished scanning, no code found
            }

            let x = 0;
            const scanX = () => {
                if (x > sourceCanvas.width - windowSize) {
                    y += stepSize;
                    setTimeout(scanY, 0); // Move to next row
                    return;
                }

                currentStep++;
                const progress = Math.round((currentStep / totalSteps) * 100);
                setScanningProgress(Math.min(progress,99));

                tempCtx.clearRect(0, 0, windowSize, windowSize);
                tempCtx.drawImage(sourceCanvas, x, y, windowSize, windowSize, 0, 0, windowSize, windowSize);
                
                try {
                    const result = cvQr.load(tempCanvas);
                    const infos = result?.getInfos();
                    if (infos && infos.length > 0 && infos[0]) {
                        console.log(`QR code detected in window at (x: ${x}, y: ${y})`);
                        setScanningProgress(100);
                        return resolve(infos[0]); // Found QR code
                    }
                } catch (err) {
                    // Ignore errors in sub-scans
                }

                x += stepSize;
                setTimeout(scanX, 0); // Move to next column
            };
            scanX();
        };
        scanY();
    });
  };
  
  const scanQrCode = async (canvas: HTMLCanvasElement): Promise<string | null> => {
    if (!cvQr) return null;
    setScanningProgress(null);

    try {
        const result = cvQr.load(canvas);
        const infos = result?.getInfos();
        if (infos && infos.length > 0 && infos[0]) {
            console.log("QR code detected in full image scan.");
            return infos[0];
        }
    } catch (err) {
        console.log("Full image scan failed or found no code, proceeding to sliding window.");
    }

    console.log("Starting sliding window scan...");
    const resultFromWindow = await scanWithSlidingWindow(canvas);
    if (resultFromWindow) {
        console.log("QR code detected using sliding window scan.");
    }
    return resultFromWindow;
  };



  const fetchRecordData = useCallback(async (sn: string) => {
    if (!authToken) return;
    setIsFetchingRecord(true);
    setFetchError(null);
    setRecordData(null);
    
    try {
      const response = await fetch(`${API_BASE_URL}/qr/record/${sn}`, {
        headers: {
            'Authorization': `Bearer ${authToken}`
        }
      });
      
      if (response.status === 404) {
        setFetchError(t('qrRecordNotFound'));
        setRecordData(null);
        return;
      }
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }
      const data: QrRecordDataResponse = await response.json();
      setRecordData(data);

    } catch (err) {
      const message = err instanceof Error ? err.message : String(err);
      setFetchError(message);
    } finally {
      setIsFetchingRecord(false);
    }
  }, [t, authToken]);



    const fetchNameplateData = useCallback(async (sn: string) => {
    if (!authToken) return;
    setIsFetchingNameplate(true);
    setNameplateError(null);
    
    try {
      const response = await fetch(`${API_BASE_URL}/qr/nameplate/${sn}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      if (response.status === 404) {
        setNameplateError(t('nameplateDataNotFound'));
        return;
      }
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }
      
      const data = await response.json();
      setNameplateData(data.data);
      setEditableNameplateData(JSON.parse(JSON.stringify(data.data))); // Deep copy
    } catch (err) {
      const message = err instanceof Error ? err.message : String(err);
      setNameplateError(message);
    } finally {
      setIsFetchingNameplate(false);
    }
  }, [authToken, t]);

  const updateNameplateData = useCallback(async (sn: string, data: string[][]) => {
    if (!authToken) return false;
    setIsUpdatingNameplate(true);
    
    try {
      const response = await fetch(`${API_BASE_URL}/qr/nameplate/${sn}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ data })
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }
      
      toast({ title: t('saveSuccessTitle'), description: t('nameplateDataUpdated') });
      setNameplateData(JSON.parse(JSON.stringify(data))); // Update original data
      return true;
    } catch (err) {
      const message = err instanceof Error ? err.message : String(err);
      toast({ title: t('saveFailedTitle'), description: message, variant: 'destructive' });
      return false;
    } finally {
      setIsUpdatingNameplate(false);
    }
  }, [authToken, toast, t]);
  
  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !captureCanvasRef.current) return;

    const video = videoRef.current;
    const canvas = captureCanvasRef.current;
    const ctx = canvas.getContext('2d');

    if (ctx && video.readyState === video.HAVE_ENOUGH_DATA) {
      // Calculate 4:3 aspect ratio dimensions
      const videoWidth = video.videoWidth;
      const videoHeight = video.videoHeight;
      const aspectRatio = 4 / 3;
      
      let cropWidth, cropHeight, cropX, cropY;
      
      if (videoWidth / videoHeight > aspectRatio) {
        // Video is wider than 4:3, crop horizontally
        cropHeight = videoHeight;
        cropWidth = videoHeight * aspectRatio;
        cropX = (videoWidth - cropWidth) / 2;
        cropY = 0;
      } else {
        // Video is taller than 4:3, crop vertically
        cropWidth = videoWidth;
        cropHeight = videoWidth / aspectRatio;
        cropX = 0;
        cropY = (videoHeight - cropHeight) / 2;
      }
      
      canvas.width = cropWidth;
      canvas.height = cropHeight;
      ctx.drawImage(video, cropX, cropY, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight);
      
      // Convert canvas to blob and create file
      canvas.toBlob((blob) => {
        if (blob) {
          const file = new File([blob], 'captured-image.jpg', { type: 'image/jpeg' });
          const imageUrl = URL.createObjectURL(blob);
          
          setImageFile(file);
          setCapturedImage(imageUrl);
          setIsLiveCapturing(false); // Stop live capture after taking photo
          stopCamera(); // Immediately stop camera
        }
      }, 'image/jpeg', 0.9);
    }
  }, [stopCamera]);

  const retakePhoto = useCallback(() => {
    if (capturedImage) {
      URL.revokeObjectURL(capturedImage);
    }
    setCapturedImage(null);
    setImageFile(null);
    setIsLiveCapturing(true); // Restart live capture
  }, [capturedImage]);

  const handleSubmit = async () => {
    if (!scannedSn || !imageFile || !authToken) return;

    setIsUploading(true);
    const formData = new FormData();
    formData.append('image', imageFile);

    try {
        const response = await fetch(`${API_BASE_URL}/qr/record/${scannedSn}/image`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
            },
            body: formData,
        });

        const data: QrRecordImageUploadResponse = await response.json();
        if (!response.ok) {
            throw new Error(data.error || t('qrUploadToS3Error'));
        }

        toast({ title: t('qrUploadSuccessTitle'), description: t('qrUploadSuccessDescription') });
        fetchRecordData(scannedSn);
        resetState(false);
    } catch (err) {
      const message = err instanceof Error ? err.message : String(err);
      toast({ title: t('errorTitle'), description: message, variant: 'destructive' });
    } finally {
        setIsUploading(false);
    }
  };

  const handlePreview = () => {
    if (!scannedSn) return;
    setPreviewImageUrl(`${API_BASE_URL}/excel/${scannedSn}/preview`);
    setIsPreviewDialogOpen(true);
  }

  const openImageViewer = (url: string) => {
    setSelectedImageUrl(url);
    setIsImageViewerOpen(true);
  };

  const openNameplateEditor = () => {
    const sn = scannedSn;
    if (!sn) return;
    setIsNameplateDialogOpen(true);
    fetchNameplateData(sn);
  };

  const handleNameplateTableChange = (rowIndex: number, colIndex: number, value: string) => {
    if (!editableNameplateData) return;
    const newData = [...editableNameplateData];
    newData[rowIndex][colIndex] = value;
    setEditableNameplateData(newData);
  };

  const addNameplateRow = () => {
    if (!editableNameplateData) return;
    const newData = [...editableNameplateData, ['', '']];
    setEditableNameplateData(newData);
  };

  const removeNameplateRow = (rowIndex: number) => {
    if (!editableNameplateData || editableNameplateData.length <= 1) return;
    const newData = editableNameplateData.filter((_, index) => index !== rowIndex);
    setEditableNameplateData(newData);
  };

  const saveNameplateData = async () => {
    const sn = scannedSn;
    if (!sn || !editableNameplateData) return;
    const success = await updateNameplateData(sn, editableNameplateData);
    if (success) {
      setIsNameplateDialogOpen(false);
    }
  };

  const cancelNameplateEdit = () => {
    setEditableNameplateData(nameplateData ? JSON.parse(JSON.stringify(nameplateData)) : null);
    setIsNameplateDialogOpen(false);
  };
  


  const scanQrFromVideo = useCallback(async () => {
    if (isLiveCapturing && videoRef.current && cameraCanvasRef.current && cvQr) {
      const video = videoRef.current;
      const canvas = cameraCanvasRef.current;
      const ctx = canvas.getContext('2d', { willReadFrequently: true });
  
      if (ctx && video.readyState === video.HAVE_ENOUGH_DATA) {
        // Calculate 4:3 aspect ratio dimensions
        const videoWidth = video.videoWidth;
        const videoHeight = video.videoHeight;
        const aspectRatio = 4 / 3;
        
        let cropWidth, cropHeight, cropX, cropY;
        
        if (videoWidth / videoHeight > aspectRatio) {
          // Video is wider than 4:3, crop horizontally
          cropHeight = videoHeight;
          cropWidth = videoHeight * aspectRatio;
          cropX = (videoWidth - cropWidth) / 2;
          cropY = 0;
        } else {
          // Video is taller than 4:3, crop vertically
          cropWidth = videoWidth;
          cropHeight = videoWidth / aspectRatio;
          cropX = 0;
          cropY = (videoHeight - cropHeight) / 2;
        }
        
        canvas.width = cropWidth;
        canvas.height = cropHeight;
        ctx.drawImage(video, cropX, cropY, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight);
        
        setFrameCount(prev => {
          const newCount = prev + 1;
          // Dynamic scan frequency: 5 frames when no SN, 30 frames when SN exists
          const scanInterval = scannedSn ? 30 : 5;
          if (newCount % scanInterval === 0) {
            try {
              // Live camera scanning should be fast, so we only use the full-frame scan.
              const result = cvQr.load(canvas);
              const infos = result?.getInfos();
              const sn = infos && infos.length > 0 ? infos[0] : null;
              
              if (sn && sn !== scannedSn) {
                setScannedSn(sn);
                // Use setTimeout to avoid setState during render
                setTimeout(() => {
                  toast({ title: t('qrScanSuccess'), description: `SN: ${sn}` });
                }, 0);
                resetState(false); // Clear previously selected photo
                fetchRecordData(sn);
              }
            } catch (err) {
              // Ignore errors, just means no QR code found in this frame
            }
          }
          return newCount;
        });
      }
    }
    if (isLiveCapturing) {
        animationFrameId.current = requestAnimationFrame(scanQrFromVideo);
    }
  }, [cvQr, fetchRecordData, isLiveCapturing, t, toast, resetState, scannedSn]);

  useEffect(() => {
    if (isLiveCapturing) {
        // Don't start camera if we already have a stream
        if (currentStreamRef.current) {
            console.log('Camera already active, skipping...');
            return;
        }
        
        // Check if getUserMedia is available
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            console.error("getUserMedia is not supported in this browser/context");
            toast({ 
                title: "Camera Error", 
                description: "Camera access is not supported in this browser or context. Please use HTTPS or a compatible browser.", 
                variant: 'destructive' 
            });
            setIsLiveCapturing(false);
            return;
        }
        
        console.log('Starting camera...');
        navigator.mediaDevices.getUserMedia({ 
          video: { 
            facingMode: 'environment',
            width: { ideal: 1280 },
            height: { ideal: 960 }
          } 
        })
            .then(stream => {
                console.log('Camera stream obtained:', stream.getTracks().map(t => `${t.kind}: ${t.label}`));
                currentStreamRef.current = stream;
                if (videoRef.current) {
                    videoRef.current.srcObject = stream;
                    videoRef.current.play();
                    setFrameCount(0);
                    animationFrameId.current = requestAnimationFrame(scanQrFromVideo);
                }
            })
            .catch(err => {
                console.error("Camera access error:", err);
                toast({ title: "Camera Error", description: "Could not access camera. Please check permissions.", variant: 'destructive' });
                setIsLiveCapturing(false);
            });
    } else {
        console.log('Stopping camera due to isLiveCapturing = false');
        stopCamera();
    }

    return () => {
        console.log('useEffect cleanup - stopping camera');
        stopCamera();
    };
  }, [isLiveCapturing, scanQrFromVideo, stopCamera, toast]);

  // Cleanup camera when component unmounts or user navigates away
  useEffect(() => {
    const handleBeforeUnload = () => {
      console.log('Page unloading - force stopping camera');
      setIsLiveCapturing(false);
      // stopCamera will be called by useEffect when isLiveCapturing changes
    };

    const handleVisibilityChange = () => {
      if (document.hidden && isLiveCapturing) {
        // Stop camera when page becomes hidden (user switches tabs/apps)
        setIsLiveCapturing(false);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      stopCamera(); // Final cleanup
    };
  }, [stopCamera, isLiveCapturing]);

  const isRecordComplete = recordData && recordData.current_image_num === recordData.required_image_num;
  const shouldShowNameplateButton = recordData && recordData.template === 'nameplate' && recordData.current_image_num === 1;

  // Stop camera when record is complete to save resources
  useEffect(() => {
    if (isRecordComplete && isLiveCapturing) {
      setIsLiveCapturing(false);
    }
  }, [isRecordComplete, isLiveCapturing]);

  if (authIsLoading || !isClient || !isAuthenticated) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen">
       <AppHeader title={t('qrRecordTitle')} />
       <main className="flex-1 flex items-center justify-center bg-muted/20 p-4">
        <canvas id="scan-canvas" ref={canvasRef} style={{ display: 'none' }} />
        <Card className="w-full max-w-4xl shadow-xl rounded-lg">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardDescription>{t('qrRecordDescription')}</CardDescription>
                {isLiveCapturing && (
                  <div className="flex items-center gap-2">
                    <Button variant="destructive" size="sm" onClick={() => {
                      setIsLiveCapturing(false);
                      stopCamera(); // Immediately stop camera
                    }}>
                      <X className="mr-2 h-4 w-4" />
                      {t('stopCapture')}
                    </Button>
                  </div>
                )}
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left side: Camera Only */}
              <div className="relative flex flex-col items-center justify-center w-full border-2 border-dashed rounded-lg bg-muted/50" style={{ aspectRatio: isLiveCapturing || capturedImage ? '4/3' : 'auto', height: isLiveCapturing || capturedImage ? 'auto' : '320px' }}>
                {isLiveCapturing ? (
                  // Live camera feed
                  <div className="relative w-full h-full rounded-lg overflow-hidden bg-black">
                    <video 
                      ref={videoRef} 
                      className="w-full h-full object-cover" 
                      autoPlay 
                      playsInline 
                      muted
                      style={{ aspectRatio: '4/3' }}
                    />
                    <canvas ref={cameraCanvasRef} className="hidden" />
                    <canvas ref={captureCanvasRef} className="hidden" />
                    
                    {/* QR Scanner indicator */}
                    {scannedSn && (
                      <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-sm">
                        QR: {scannedSn}
                      </div>
                    )}
                    
                    {/* Capture button */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                      <Button 
                        onClick={capturePhoto}
                        className="bg-white text-black hover:bg-gray-200 rounded-full w-16 h-16"
                        disabled={!scannedSn}
                      >
                        <Camera className="h-6 w-6" />
                      </Button>
                    </div>
                  </div>
                ) : capturedImage ? (
                  // Captured image preview
                  <div className="relative w-full h-full">
                    <Image 
                      src={capturedImage} 
                      alt={t('capturedImageAlt')} 
                      fill 
                      style={{objectFit: "contain"}} 
                      className="rounded-lg" 
                    />
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                      <Button 
                        onClick={retakePhoto}
                        variant="outline"
                        className="bg-white/90 hover:bg-white"
                      >
                        {t('retakePhoto')}
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Default state - clickable area to start camera
                  <div
                    className="text-center text-muted-foreground w-full h-full flex flex-col items-center justify-center cursor-pointer hover:bg-muted/70 transition-colors duration-200 rounded-lg"
                    onClick={() => {
                      if (!isFetchingRecord && !isUploading) {
                        if (cvQr) {
                          setIsLiveCapturing(true);
                          setRecordData(null);
                          setScannedSn(null);
                        } else if (cvQrInitError) {
                          // Retry initialization if it failed
                          initializeOpencvQr();
                        }
                      }
                  }}
                  >
                    <Camera className="h-12 w-12 mx-auto mb-4" />
                    {cvQrInitError ? (
                      <>
                        <p className="font-semibold text-destructive">{t('qrScannerInitError')}</p>
                        <p className="text-xs">{t('clickToRetryInit')}</p>
                        {cvQrRetryCount > 0 && (
                          <p className="text-xs mt-1">({t('retryAttempt')} {cvQrRetryCount}/3)</p>
                        )}
                      </>
                    ) : !cvQr ? (
                      <>
                        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                        <p className="font-semibold">{t('initializingQrScanner')}</p>
                        <p className="text-xs">{t('pleaseWait')}</p>
                      </>
                    ) : (
                      <>
                        <p className="font-semibold">{t('clickToStartCapture')}</p>
                        <p className="text-xs">{t('realTimeScanAndCapture')}</p>
                      </>
                    )}
                  </div>
                )}
              </div>

              {/* Right side: Data */}
              <div className="flex flex-col space-y-4">
                {(isFetchingRecord) && (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                )}

                {fetchError && !isFetchingRecord && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>{t('errorTitle')}</AlertTitle>
                    <CardDescription>{fetchError}</CardDescription>
                  </Alert>
                )}

                {recordData && !isFetchingRecord && (
                  <Card>
                    <CardHeader className='pb-4'>
                       <CardTitle className="text-lg">{t('qrRecordDetailsTitle')}</CardTitle>
                       <CardDescription className='font-mono pt-1'>{scannedSn}</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div><span className="font-semibold text-muted-foreground">{t('company')}:</span> {recordData.company}</div>
                      <div><span className="font-semibold text-muted-foreground">{t('area')}:</span> {recordData.area}</div>
                      <div><span className="font-semibold text-muted-foreground">{t('item')}:</span> {recordData.item}</div>
                      <div><span className="font-semibold text-muted-foreground">{t('selectTemplate')}:</span> {recordData.template}</div>
                      <div>
                        <span className="font-semibold text-muted-foreground">{t('status')}:</span> 
                        <span className={`ml-2 font-medium ${isRecordComplete ? 'text-green-600' : 'text-blue-600'}`}>
                           {recordData.current_image_num} / {recordData.required_image_num} {t('images')}
                        </span>
                      </div>
                      
                      {isRecordComplete && (
                          <Alert variant="default" className="bg-green-50 border-green-200">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <AlertTitle className="text-green-800">{t('qrRecordCompleteTitle')}</AlertTitle>
                              <CardDescription className="text-green-700">{t('qrRecordCompleteDescription')}</CardDescription>
                          </Alert>
                      )}

                      <div className="flex flex-wrap gap-2 pt-2">
                        {recordData.image_urls?.map((url, index) => (
                           <button key={index} onClick={() => openImageViewer(url)} className="focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-md">
                             <Image src={url} alt={t('uploadedImageAlt', { index: index + 1 })} width={100} height={100} className="rounded-md border object-cover" data-ai-hint="machinery part" />
                           </button>
                        ))}
                      </div>

                    </CardContent>
                    <CardFooter className="flex-col items-stretch gap-2">
                      <Button onClick={handleSubmit} disabled={isUploading || isRecordComplete || !imageFile || !!fetchError}>
                        {isUploading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Upload className="mr-2 h-4 w-4" />}
                        {t('submit')}
                      </Button>
                      <Button onClick={handlePreview} variant="outline" disabled={isUploading || !recordData.preview_available}>
                        <Eye className="mr-2 h-4 w-4"/>
                        {t('qrPreviewButton')}
                      </Button>
                      {shouldShowNameplateButton && (
                        <Button onClick={openNameplateEditor} variant="outline" className="bg-blue-50 hover:bg-blue-100 border-blue-200">
                          <Edit3 className="mr-2 h-4 w-4"/>
                          {t('viewEditNameplateButton')}
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
      

      <Dialog open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
        <DialogContent className="p-0 m-0 w-screen h-screen max-w-none border-none bg-black/80">
          <DialogHeader className="sr-only">
            <DialogTitle>{t('qrPreviewTitle')}</DialogTitle>
          </DialogHeader>
          <div className="w-full h-full flex items-center justify-center p-4" onClick={() => setIsPreviewDialogOpen(false)}>
            {previewImageUrl ? (
                <Image src={previewImageUrl} alt={t('qrPreviewAlt', { sn: scannedSn || '' })} fill style={{objectFit: 'contain'}} data-ai-hint="spreadsheet document"/>
            ) : (
                <div className="flex items-center justify-center h-64 text-muted-foreground">
                    <Loader2 className="h-8 w-8 animate-spin"/>
                </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
      
      <Dialog open={isImageViewerOpen} onOpenChange={setIsImageViewerOpen}>
        <DialogContent className="p-0 m-0 w-screen h-screen max-w-none border-none bg-black/80">
          <DialogHeader className="sr-only"><DialogTitle>{t('imagePreviewAlt')}</DialogTitle></DialogHeader>
            {selectedImageUrl && (
              <div className="w-full h-full flex items-center justify-center p-4" onClick={() => setIsImageViewerOpen(false)}>
                <Image src={selectedImageUrl} alt={t('imagePreviewAlt')} fill style={{objectFit: 'contain'}} />
              </div>
            )}
        </DialogContent>
      </Dialog>

      {/* Nameplate Editor Dialog */}
      <Dialog open={isNameplateDialogOpen} onOpenChange={setIsNameplateDialogOpen}>
        <DialogContent className="p-0 m-0 w-screen h-screen max-w-none border-none bg-white">
          <DialogHeader className="p-6 border-b bg-gray-50">
            <DialogTitle className="text-xl font-semibold">{t('nameplateEditorTitle', { sn: scannedSn || '' })}</DialogTitle>
          </DialogHeader>
          
          <div className="flex-1 overflow-auto p-6">
            {isFetchingNameplate && (
              <div className="flex items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            )}
            
            {nameplateError && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>{t('errorTitle')}</AlertTitle>
                <CardDescription>{nameplateError}</CardDescription>
              </Alert>
            )}
            
            {editableNameplateData && !isFetchingNameplate && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">{t('nameplateDataGridTitle')}</h3>
                  <Button onClick={addNameplateRow} variant="outline" size="sm">
                    {t('addRowButton')}
                  </Button>
                </div>
                
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <tbody>
                      {editableNameplateData.map((row, rowIndex) => (
                        <tr key={rowIndex} className="border-t hover:bg-gray-50 first:border-t-0">
                          <td className="px-2 md:px-4 py-3 border-r w-1/2">
                            <Textarea
                              value={row[0] || ''}
                              onChange={(e) => {
                                handleNameplateTableChange(rowIndex, 0, e.target.value);
                                // Auto-resize textarea
                                e.target.style.height = 'auto';
                                e.target.style.height = e.target.scrollHeight + 'px';
                              }}
                              className="border-none shadow-none focus:ring-1 focus:ring-blue-500 w-full resize-none min-h-[40px] overflow-hidden"
                              placeholder={t('nameplateKeyPlaceholder')}
                              rows={1}
                              style={{ height: 'auto' }}
                              onInput={(e) => {
                                e.target.style.height = 'auto';
                                e.target.style.height = e.target.scrollHeight + 'px';
                              }}
                            />
                          </td>
                          <td className="px-2 md:px-4 py-3 border-r w-1/2">
                            <Textarea
                              value={row[1] || ''}
                              onChange={(e) => {
                                handleNameplateTableChange(rowIndex, 1, e.target.value);
                                // Auto-resize textarea
                                e.target.style.height = 'auto';
                                e.target.style.height = e.target.scrollHeight + 'px';
                              }}
                              className="border-none shadow-none focus:ring-1 focus:ring-blue-500 w-full resize-none min-h-[40px] overflow-hidden"
                              rows={1}
                              style={{ height: 'auto' }}
                              onInput={(e) => {
                                e.target.style.height = 'auto';
                                e.target.style.height = e.target.scrollHeight + 'px';
                              }}
                            />
                          </td>
                          <td className="px-1 py-3 text-center min-w-[60px]">
                            {editableNameplateData && editableNameplateData.length > 1 && (
                              <Button
                                onClick={() => removeNameplateRow(rowIndex)}
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-700 hover:bg-red-50 min-w-[40px]"
                              >
                                <XCircle className="h-4 w-4" />
                              </Button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
          
          <div className="p-6 border-t bg-gray-50 flex justify-end gap-3">
            <Button onClick={cancelNameplateEdit} variant="outline">
              <XCircle className="mr-2 h-4 w-4" />
              {t('cancelButton')}
            </Button>
            <Button 
              onClick={saveNameplateData} 
              disabled={isUpdatingNameplate || !editableNameplateData}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isUpdatingNameplate ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
              {t('saveButton')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <Toaster />
    </div>
  );
}
