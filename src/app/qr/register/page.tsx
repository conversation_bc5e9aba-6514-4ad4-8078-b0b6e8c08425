
'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import Image from 'next/image';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Toaster } from "@/components/ui/toaster";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/contexts/auth-context';
import { useRouter } from 'next/navigation';
import { useTranslations } from '@/hooks/use-translations';
import { Loader2, Download, Languages } from 'lucide-react';
import { ComboboxInput } from '@/components/combobox-input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import type { Language, Template, GetTemplateResponse, QrRegisterPayload, QrRegisterResponse, ImageUrlResponse } from '@/types';
import { getHistory, saveHistory } from '@/lib/localstorage-history';
import { AppHeader } from '@/components/app-header';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";
const COMPANY_HISTORY_KEY = 'history_qr_register_company';
const AREA_HISTORY_KEY = 'history_qr_register_area';
const HISTORY_LIMIT = 10;

//qr_records 
export default function QrRegisterPage() {
  const { t, language, setLanguage, isEnabled: isTranslationEnabled } = useTranslations();
  const { toast } = useToast();
  const { token: authToken, logout, isLoading: authIsLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  // Form State
  const [company, setCompany] = useState('');
  const [area, setArea] = useState('');
  const [item, setItem] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('');

  // History State
  const [companyOptions, setCompanyOptions] = useState<string[]>([]);
  const [areaOptions, setAreaOptions] = useState<string[]>([]);

  // Template State
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(true);

  // UI State
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [generatedSN, setGeneratedSN] = useState<string | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const qrImageRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Redirect if not authenticated
  useEffect(() => {
    if (isClient && !authIsLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isClient, authIsLoading, isAuthenticated, router]);
  
  // Load history and set defaults on client mount
  useEffect(() => {
    if (isClient) {
      const companyHistory = getHistory(COMPANY_HISTORY_KEY, HISTORY_LIMIT);
      const areaHistory = getHistory(AREA_HISTORY_KEY, HISTORY_LIMIT);
      
      setCompanyOptions(companyHistory);
      setAreaOptions(areaHistory);

      if (companyHistory.length > 0) {
        setCompany(companyHistory[0]);
      }
      if (areaHistory.length > 0) {
        setArea(areaHistory[0]);
      }
    }
  }, [isClient]);

  const fetchTemplates = useCallback(async () => {
    if (!authToken) return;
    setIsLoadingTemplates(true);
    try {
        const response = await fetch(`${API_BASE_URL}/get_template`, {
            headers: { 
                'Authorization': `Bearer ${authToken}`,
                'Accept-Language': language,
            },
        });
        if (response.status === 401) { logout(); return; }
        const data: GetTemplateResponse = await response.json();
        if (!response.ok) throw new Error(data.error || t('fetchTemplateError'));
        
        setTemplates(data.template_list || []);
        if (data.template_list && data.template_list.length > 0) {
          setSelectedTemplate(data.template_list[0].name);
        }
    } catch (error) {
        console.error("Error fetching templates:", error);
        setTemplates([]);
        const errorMessage = error instanceof Error ? error.message : t('fetchTemplateError');
        toast({ title: t('errorTitle'), description: errorMessage, variant: 'destructive' });
    } finally {
        setIsLoadingTemplates(false);
    }
  }, [authToken, language, logout, t, toast]);

  useEffect(() => {
    if (isClient && isAuthenticated) {
      fetchTemplates();
    }
  }, [isClient, isAuthenticated, fetchTemplates]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!company || !area || !item || !selectedTemplate) {
      toast({ title: t('errorTitle'), description: t('missingInfoDescription'), variant: "destructive" });
      return;
    }
    setIsSubmitting(true);
    setGeneratedSN(null);
    setQrCodeUrl(null);

    // Save to history
    if (isClient) {
      saveHistory(COMPANY_HISTORY_KEY, company, HISTORY_LIMIT);
      saveHistory(AREA_HISTORY_KEY, area, HISTORY_LIMIT);
       setCompanyOptions(getHistory(COMPANY_HISTORY_KEY, HISTORY_LIMIT));
       setAreaOptions(getHistory(AREA_HISTORY_KEY, HISTORY_LIMIT));
    }
    
    const payload: QrRegisterPayload = { company, area, item, template: selectedTemplate };
    
    try {
      const response = await fetch(`${API_BASE_URL}/qr/register`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
          'Accept-Language': language,
        },
        body: JSON.stringify(payload)
      });

      const data: QrRegisterResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to register serial number.');
      }
      
      setGeneratedSN(data.sn);
      
      // Fetch the signed URL for the QR code
      const imageUrlResponse = await fetch(`${API_BASE_URL}/image/sn/${data.sn}`);
      const imageUrlData: ImageUrlResponse = await imageUrlResponse.json();
      if (!imageUrlResponse.ok || !imageUrlData.url) {
        throw new Error(imageUrlData.error || 'Failed to get QR code image URL.');
      }
      setQrCodeUrl(imageUrlData.url);

      toast({ title: "Success", description: `Serial number ${data.sn} registered.`, variant: 'default', className: "bg-accent text-accent-foreground"});

    } catch (err) {
      const message = err instanceof Error ? err.message : String(err);
      toast({ title: t('errorTitle'), description: message, variant: 'destructive' });
      setQrCodeUrl(null);
    } finally {
      setIsSubmitting(false);
    }
  };
  const handleDownload = async () => {
    if (!qrCodeUrl) return;
    window.open(qrCodeUrl)
  };

  if (authIsLoading || !isClient) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-col h-screen" id="qr-register-page">
        <AppHeader title={t('qrRegisterTitle')} />
        <div className="flex-1 flex items-center justify-center bg-muted/20 p-4">
            <Card className="w-full max-w-xl shadow-xl rounded-lg">
              <CardHeader>
                <CardDescription>
                  {t('qrRegisterDescription')}
                  <br />
                  <span className="text-sm text-muted-foreground mt-2 block">{t('qrSameItemWarning')}</span>
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                {qrCodeUrl ? (
                  <div className="space-y-4 text-center">
                    <div className="flex justify-center">
                       <Image 
                         ref={qrImageRef}
                         src={qrCodeUrl}
                         alt={`QR Code for ${generatedSN}`} 
                         width={400} 
                         height={400} 
                         data-ai-hint="qr code"
                         crossOrigin="anonymous" // Important for canvas operations
                       />
                    </div>
                    <div className="flex gap-2 justify-center">
                       <Button variant="outline" onClick={() => { setQrCodeUrl(null); }}>{t('registerAnother')}</Button>
                       <Button onClick={handleDownload}><Download className="mr-2 h-4 w-4" />{t('downloadButton')}</Button>
                    </div>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-1">
                      <Label htmlFor="company">{t('company')}</Label>
                      <ComboboxInput
                        options={companyOptions}
                        value={company}
                        onChange={setCompany}
                        placeholder={t('selectOrEnter')}
                        inputPlaceholder={t('searchOrEnter')}
                        disabled={isSubmitting}
                        aria-label={t('company')}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="area">{t('area')}</Label>
                       <ComboboxInput
                        options={areaOptions}
                        value={area}
                        onChange={setArea}
                        placeholder={t('selectOrEnter')}
                        inputPlaceholder={t('searchOrEnter')}
                        disabled={isSubmitting}
                        aria-label={t('area')}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="item">{t('item')}</Label>
                      <Input
                        id="item"
                        value={item}
                        onChange={(e) => setItem(e.target.value)}
                        placeholder={t('item')}
                        required
                        disabled={isSubmitting}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="template">{t('selectTemplate')}</Label>
                      {isLoadingTemplates ? (
                        <Skeleton className="h-10 w-full" />
                      ) : (
                        <Select onValueChange={setSelectedTemplate} value={selectedTemplate} disabled={isSubmitting || templates.length === 0}>
                          <SelectTrigger id="template">
                            <SelectValue placeholder={t('templateSelectPlaceholder')} />
                          </SelectTrigger>
                          <SelectContent>
                            {templates.map((template) => (
                              <SelectItem key={template.name} value={template.name}>{template.name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    </div>
                    <Button type="submit" className="w-full" disabled={isSubmitting || isLoadingTemplates}>
                      {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                      {t('qrRegisterSubmitButton')}
                    </Button>
                  </form>
                )}
              </CardContent>
            </Card>
        </div>
      </div>
      <Toaster />
    </>
  );
}
