
'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link'; // Import Link
import { useRouter } from 'next/navigation';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from '@/contexts/auth-context';
import { useTranslations } from '@/hooks/use-translations';
import { Loader2, Languages, UserCog } from 'lucide-react'; // Changed UserShield to UserCog
import { Toaster } from "@/components/ui/toaster";
import { useToast } from '@/hooks/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { Language } from '@/types';

export default function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const { login, isLoading: authIsLoading, isAuthenticated } = useAuth();
  const { t, language, setLanguage, isEnabled: isTranslationEnabled } = useTranslations();
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!authIsLoading && isAuthenticated) {
      router.push('/');
    }
  }, [authIsLoading, isAuthenticated, router]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!username || !password) {
        toast({
            title: t('loginErrorTitle'),
            description: t('loginErrorInvalid'),
            variant: "destructive",
        });
        return;
    }
    setIsSubmitting(true);
    await login(username, password);
    setIsSubmitting(false);
  };

  const handleLanguageChange = (lang: Language) => {
    setLanguage(lang);
  };

  if (authIsLoading || (!authIsLoading && isAuthenticated)) {
      return (
          <div className="flex min-h-screen items-center justify-center bg-background p-4">
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
          </div>
      );
  }

  return (
    <>
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Card className="w-full max-w-sm shadow-xl rounded-lg">
          <CardHeader className="text-center relative">
            <CardTitle className="text-2xl font-bold">{t('loginTitle')}</CardTitle>
            <CardDescription>{t('loginDescription')}</CardDescription>
            {isTranslationEnabled && (
              <div className="absolute top-4 right-4">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <Languages className="h-5 w-5" />
                      <span className="sr-only">{t('changeLanguage')}</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleLanguageChange('en')} disabled={language === 'en'}>
                      {t('languageNameEn')}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleLanguageChange('zh')} disabled={language === 'zh'}>
                      {t('languageNameZh')}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleLanguageChange('ko')} disabled={language === 'ko'}>
                      {t('languageNameKo')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">{t('username')}</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder={t('username')}
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  disabled={isSubmitting || authIsLoading}
                  className="bg-input/50"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">{t('password')}</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder={t('password')}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={isSubmitting || authIsLoading}
                  className="bg-input/50"
                />
              </div>
            </CardContent>
            <CardFooter className="flex-col items-stretch">
              <Button type="submit" className="w-full" disabled={isSubmitting || authIsLoading}>
                {(isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t('loginButton')}
              </Button>
              <div className="mt-4 text-center">
                <Link href="/admin/login" className="text-sm text-primary hover:underline flex items-center justify-center">
                  <UserCog className="mr-1 h-4 w-4" /> {/* Changed UserShield to UserCog */}
                  {t('switchToAdminLogin')}
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>
      </div>
      <Toaster />
    </>
  );
}
