
'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { getToken as getTokenFromStorage, saveToken as saveTokenToStorage, removeToken as removeTokenFromStorage } from '@/lib/token';
import { useToast } from '@/hooks/use-toast';
import { useTranslations } from '@/hooks/use-translations';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";

interface AuthContextType {
  token: string | null;
  login: (usernameInput: string, passwordInput: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [token, setToken] = useState<string | null>(null);
  const [authIsLoading, setAuthIsLoading] = useState(true);
  const [isVerifying, setIsVerifying] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { toast, dismiss } = useToast();
  const { t, language } = useTranslations();

  const logout = useCallback(() => {
    const tokenToInvalidate = getTokenFromStorage();

    // Immediately update UI and local storage for a snappy user experience
    removeTokenFromStorage();
    setToken(null);
    if (pathname !== '/login') {
      router.push('/login');
    }

    // If a token existed, make a best-effort attempt to invalidate it on the server
    if (tokenToInvalidate) {
      fetch(`${API_BASE_URL}/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${tokenToInvalidate}`,
          'Content-Type': 'application/json',
          'Accept-Language': language,
        },
      }).catch(error => {
        console.error('Logout API call failed:', error);
      });
    }
  }, [router, pathname, language]);

  const login = useCallback(async (usernameInput: string, passwordInput: string): Promise<boolean> => {
    setAuthIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Accept-Language': language },
        body: JSON.stringify({ username: usernameInput, password: passwordInput }),
      });
      const responseData = await response.json();

      if (!response.ok) {
        toast({
          title: t('loginErrorTitle'),
          description: responseData.message || t('loginErrorInvalid'),
          variant: 'destructive',
        });
        setAuthIsLoading(false);
        return false;
      }

      if (responseData.token) {
        saveTokenToStorage(responseData.token);
        setToken(responseData.token); // Update token state, which will trigger other effects
        router.push('/'); // Redirect to home page
        // authIsLoading will be managed by the effects on the new page/route
        return true;
      } else {
        toast({
          title: t('loginErrorTitle'),
          description: responseData.message || t('loginErrorMissingToken'),
          variant: 'destructive',
        });
        setAuthIsLoading(false);
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      toast({
        title: t('loginErrorTitle'),
        description: t('loginErrorNetwork'),
        variant: 'destructive',
      });
      setAuthIsLoading(false);
      return false;
    }
  }, [router, toast, t, language]);

  // Effect for initial and ongoing authentication management
  useEffect(() => {
    let isMounted = true;
    let verificationToastId: string | null = null;
    
    // Function to verify token, with different behavior for initial vs. background checks
    const verifyToken = async (tokenToVerify: string, isInitial: boolean) => {
      if (isVerifying) return; // Prevent concurrent requests
      if (isMounted) setIsVerifying(true);

      if (isInitial && isMounted) {
        const { id } = toast({
          title: t('verifyingUserInfoTitle'),
          description: t('verifyingUserInfoDescription'),
          duration: Infinity,
        });
        verificationToastId = id;
      }

      try {
        const response = await fetch(`${API_BASE_URL}/check-verify`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${tokenToVerify}`,
            'Accept-Language': language,
          },
        });
        
        if (verificationToastId) {
          dismiss(verificationToastId);
          verificationToastId = null;
        }

        if (!response.ok && isMounted) {
          if (response.status === 401) {
            if (isInitial) { // Show toast only for initial failure that leads to logout
                toast({ title: t('sessionExpiredTitle'), description: t('sessionExpiredDescription'), variant: 'destructive' });
            }
            logout();
          }
        }
      } catch (error) {
        if (verificationToastId) {
          dismiss(verificationToastId);
          verificationToastId = null;
        }
        console.error('Network error during token verification:', error);
      } finally {
        if (isMounted) {
            setIsVerifying(false);
        }
      }
    };
    
    // Initial, non-blocking check on mount
    const storedToken = getTokenFromStorage();
    if (storedToken) {
      setToken(storedToken); // Immediately set token to render authenticated UI
      verifyToken(storedToken, true); // Verify in background without blocking
    }
    setAuthIsLoading(false); // Unblock rendering immediately

    // Setup periodic and visibility-based verification for ongoing checks
    const runBackgroundVerification = () => {
        const currentToken = getTokenFromStorage();
        if (currentToken) {
            // This is a background check, so no toast
            verifyToken(currentToken, false); 
        } else if (token) {
            // If component state has a token but storage doesn't (e.g., logout in another tab)
            logout(); 
        }
    };

    const handleVisibilityChange = () => {
        if (document.visibilityState === 'visible') {
            runBackgroundVerification();
        }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    const intervalId = setInterval(runBackgroundVerification, 600000); // Check every 10 minutes

    return () => {
      isMounted = false;
      if (verificationToastId) dismiss(verificationToastId);
      clearInterval(intervalId);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [logout, language, toast, dismiss, t, token]);


  return (
    <AuthContext.Provider value={{ token, login, logout, isLoading: authIsLoading, isAuthenticated: !!token }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
