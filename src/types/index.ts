
export interface SingleImageData {
  id: string;
  file: File | null;
  previewUrl: string | null;
  status: string;
  isProcessing: boolean;
  needsProcessing: boolean;
  error: string | null;
  angle?: number;
  isStatusFixed?: boolean;
}

export interface ProcessImagePayload {
    images: {
        id: string;
        imageData: string;
        angle: number;
    }[];
}

export interface ImageResult {
    id: string;
    success: boolean;
    status?: string;
    error?: string;
    angle?: number;
}

export interface ProcessImageResponse {
    error?: string;
    company?: string;
    item?: string;
    area?: string;
    results?: ImageResult[];
}

export interface FileLink {
  filename: string;
  url: string;
}

export interface ExcelPayload {
  company?: string;
  item?: string;
  area?: string;
  images: {
    fileName: string;
    status?: string;
    imageData: string;
    angle?: number;
  }[];
  template?: string; // Changed from templates array
  data?: string[][];
  record_uuid?: string;
}

export interface ExcelResponse {
    files?: FileLink[];
    message?: string; // for errors
}

export interface SingleImageProcessResponse {
  data: string[][];
  error?: string;
  item_image_angle?: number;
  nameplate_image_angle?: number;
  company?: string;
  item?: string;
  area?: string;
}

export interface DropdownOptions {
  company: string[];
  area: string[];
  item: string[];
  status: string[];
}

// Template types
export interface Template {
  name: string;
  template_name: string;
  images_number: number;
  fixed_status: string[];
}

export interface GetTemplateResponse {
  template_list: Template[];
  error?: string;
}

// QR Register Types
export interface QrRegisterPayload {
    company: string;
    area: string;
    item: string;
    template: string;
}

export interface QrRegisterResponse {
    sn: string;
    error?: string;
}

// QR Record Types
export interface QrRecordDataResponse {
  company: string;
  area: string;
  item: string;
  template: string;
  current_image_num: number;
  required_image_num: number;
  image_urls: string[];
  preview_available: boolean;
  error?: string;
}

export interface QrRecordImageUploadResponse {
    message: string;
    image_number: number;
    current_image_num: number;
    required_image_num: number;
    error?: string;
}

export interface ImageUrlResponse {
  url: string;
  error?: string;
}


// Staging Types
export interface StagedRecordSummary {
  record_name: string;
  uuid: string;
  datetime: string;
  type: 'record' | 'nameplate';
}

export interface StagedRecordsListResponse {
  data: StagedRecordSummary[];
  error?: string;
}

export interface StagedRecordDataResponse extends Omit<ExcelPayload, 'template'> {
  template: string;
  error?: string;
}


// Admin Panel Specific Types
export interface AdminInfo {
  username: string;
  email: string;
  organization: string;
  expire_time: string; // ISO date string
  is_expired: boolean;
}

export interface AdminLoginResponse {
  token: string;
  user_type: 'admin';
  admin_info?: AdminInfo;
  error?: string;
}

export interface EndUser {
  id: number;
  username: string;
  email: string;
  is_disabled: boolean;
  created_at: string; // ISO date string
}

export interface UserSummary {
  total_users: number;
  active_users: number;
  disabled_users: number;
  quota_limit: number;
  quota_used: number;
  quota_remaining: number;
}

export interface AdminUsersResponse {
  users: EndUser[];
  summary: UserSummary;
  error?: string;
}

export interface AddUserPayload {
  username: string;
  password?: string;
  email: string;
}

export interface UpdateUserEmailPayload {
  email: string;
}

export interface UpdateUserPasswordPayload {
  password: string;
}

export interface UpdateUserStatusPayload {
  is_disabled: boolean;
}

export interface AdminApiResponse {
    message?: string;
    error?: string;
    signed_url?: string;
}

// Admin Records Types
export interface RecordItem {
  uuid: string;
  end_user_username: string;
  record_name: string;
  created_at: string; // ISO date string
}

export interface AdminRecordsResponse {
  records: RecordItem[];
  error?: string;
}

// Admin Email Configuration Types
export interface EmailConfigGetResponse {
  has_config: boolean;
  enabled: boolean;
  sender_email: string | null;
  error?: string;
}

export interface EmailConfigUpdatePayload {
  sender_email?: string;
  sender_password?: string;
  smtp_host?: string;
  smtp_port?: number;
  enable: boolean;
}

export interface EmailConfigTestResponse {
  message?: string;
  recipient?: string;
  sent_time?: string; // ISO date string or formatted string
  error?: string;
  details?: string;
}


export type TranslationKey =
  | 'appName'
  | 'headerInfo'
  | 'company'
  | 'area'
  | 'item'
  | 'images'
  | 'uploadImages'
  | 'uploadDisabled'
  | 'maxImages'
  | 'addMore'
  | 'photoNumber'
  | 'imagePreviewAlt'
  | 'status'
  | 'submit'
  | 'submitting'
  | 'submitSuccess'
  | 'submitSuccessMessage'
  | 'submitSuccessAndSent'
  | 'submitError'
  | 'uploading'
  | 'processingImages'
  | 'processImages'
  | 'processingNeeded'
  | 'uploadError'
  | 'selectOption'
  | 'selectOrEnter'
  | 'orEnterValue'
  | 'searchOrEnter'
  | 'removeImage'
  | 'removeSlot'
  | 'changeLanguage'
  | 'english'
  | 'chinese'
  | 'korean'
  | 'languageNameEn'
  | 'languageNameZh'
  | 'languageNameKo'
  | 'optionsLoadError'
  | 'errorTitle'
  | 'optionsLoadErrorDescription'
  | 'noImagesToProcessTitle'
  | 'noImagesToProcessDescription'
  | 'processingCompleteTitle'
  | 'processingCompleteDescription'
  | 'batchProcessingErrorTitle'
  | 'cannotRemoveLastSlotTitle'
  | 'cannotRemoveLastSlotDescription'
  | 'invalidMoveTitle'
  | 'invalidMoveDescription'
  | 'orderChangedTitle'
  | 'orderChangedDescription'
  | 'missingInfoTitle'
  | 'missingInfoDescription'
  | 'processingRequiredTitle'
  | 'waitForProcessingDescription'
  | 'missingValidImagesTitle'
  | 'missingValidImagesDescription'
  | 'missingStatusTitle'
  | 'missingStatusDescription'
  | 'imageErrorsTitle'
  | 'imageErrorsDescription'
  | 'notApplicable'
  | 'comboboxInputLabel'
  | 'noResultsFound'
  | 'dragToReorder'
  | 'cannotDragEmptySlot'
  | 'loading'
  | 'retry'
  | 'notAssigned'
  | 'noStatusFound'
  | 'statusInputLabel'
  | 'invalidApiResponse'
  | 'apiNoResultError'
  | 'unknownProcessingError'
  | 'unknownBatchError'
  | 'invalidExcelApiResponse'
  // Login keys
  | 'loginTitle'
  | 'loginDescription'
  | 'username'
  | 'password'
  | 'loginButton'
  | 'logoutButton'
  | 'loginErrorTitle'
  | 'loginErrorInvalid'
  | 'loginErrorMissingToken'
  | 'loginErrorNetwork'
  | 'switchToAdminLogin'
  | 'switchToUserLogin'
  // Session/Verification keys
  | 'sessionExpiredTitle'
  | 'sessionExpiredDescription'
  | 'networkErrorTitle'
  | 'networkErrorDescription'
  | 'networkErrorOnVerify'
  | 'verifyingUserInfoTitle'
  | 'verifyingUserInfoDescription'
  // Geolocation keys
  | 'currentLocationTitle'
  | 'refreshLocation'
  | 'fetchingLocation'
  | 'latitude'
  | 'longitude'
  | 'address'
  | 'fetchingAddress'
  | 'addressNotFound'
  | 'addressNotAvailable'
  | 'geocodingApiKeyMissing'
  | 'geocodingApiKeyMissingFull'
  | 'geocodingError'
  | 'geolocationNotSupported'
  | 'geolocationPermissionDenied'
  | 'geolocationPositionUnavailable'
  | 'geolocationTimeout'
  | 'geolocationUnknownError'
  | 'locationErrorTitle'
  | 'addressInputPlaceholder'
  | 'addressHistoryEmpty'
  | 'latitudeLabel'
  | 'longitudeLabel'
  // Admin Panel Keys
  | 'adminLoginTitle'
  | 'adminLoginDescription'
  | 'adminLoginButton'
  | 'adminLoginButtonTitle'
  | 'adminDashboardTitle'
  | 'adminUserManagement'
  | 'adminLogoutButton'
  | 'adminAddUser'
  | 'adminEditUser'
  | 'adminEmail'
  | 'adminActions'
  | 'adminStatus'
  | 'adminStatusActive'
  | 'adminStatusDisabled'
  | 'adminCreatedAt'
  | 'adminConfirmPassword'
  | 'adminPasswordMismatch'
  | 'adminPasswordMinLengthError'
  | 'adminPasswordNumericOnlyError'
  | 'adminAddUserSuccess'
  | 'adminAddUserError'
  | 'adminUpdateEmailSuccess'
  | 'adminUpdateEmailError'
  | 'adminUpdatePasswordSuccess'
  | 'adminUpdatePasswordError'
  | 'adminUpdateStatusSuccess'
  | 'adminUpdateStatusError'
  | 'adminUserNotFoundError'
  | 'adminEmailExistsError'
  | 'adminUsernameExistsError'
  | 'adminQuotaExceededError'
  | 'adminGenericError'
  | 'adminAccountExpiredError'
  | 'adminInvalidCredentialsError'
  | 'adminAuthRequiredError'
  | 'adminUserSummaryTitle'
  | 'adminTotalUsers'
  | 'adminActiveUsers'
  | 'adminDisabledUsers'
  | 'adminQuotaLimit'
  | 'adminQuotaUsed'
  | 'adminQuotaRemaining'
  | 'adminConfirmDisableUserTitle'
  | 'adminConfirmEnableUserTitle'
  | 'adminConfirmDisableUserMessage'
  | 'adminConfirmEnableUserMessage'
  | 'adminCancel'
  | 'adminConfirm'
  | 'adminEditEmailTitle'
  | 'adminEditEmailMultipleHint'
  | 'adminMaxEmailsError'
  | 'adminEditPasswordTitle'
  | 'adminNewPassword'
  | 'adminSaveChanges'
  | 'adminSaving'
  | 'adminUsersList'
  | 'adminEnableUser'
  | 'adminDisableUser'
  | 'adminLoadingUsers'
  | 'adminNoUsersFound'
  | 'adminNoActiveUsersFound'
  | 'adminFetchingUsersError'
  | 'adminAccountDetailsTitle'
  | 'adminOrganization'
  | 'adminAccountExpireTime'
  | 'adminAccountStatus'
  | 'adminStatusExpired'
  | 'adminStatusNotExpired'
  | 'adminAccountInfoNotAvailable'
  | 'adminShowDisabledUsers'
  | 'adminRemainingDays'
  // Admin Records Keys
  | 'adminRecentRecordsTitle'
  | 'adminLoadingRecords'
  | 'adminNoRecordsFound'
  | 'adminFetchingRecordsError'
  | 'adminRecordName'
  | 'adminDownloadError'
  | 'adminRecordNotFound'
  | 'adminSearchRecordsPlaceholder'
  // Form Actions
  | 'clearFormButton'
  | 'formClearedToastTitle'
  | 'formClearedToastDescription'
  // Admin Email Configuration
  | 'adminSenderEmailLabel'
  | 'adminSystemDefaultSender'
  | 'adminConfigureEmailTitle'
  | 'adminEnableCustomEmailConfig'
  | 'adminSenderPasswordLabel'
  | 'adminSenderPasswordLabel'
  | 'adminSmtpHostLabel'
  | 'adminSmtpPortLabel'
  | 'adminSaveEmailConfigButton'
  | 'adminTestEmailConfigButton'
  | 'adminTestingEmailConfig'
  | 'adminSavingEmailConfig'
  | 'adminEmailConfigFetchError'
  | 'adminEmailConfigSaveSuccess'
  | 'adminEmailConfigSaveError'
  | 'adminEmailConfigTestSuccess'
  | 'adminEmailConfigTestError'
  | 'adminEmailConfigTestErrorGeneric'
  | 'adminEmailConfigDisabled'
  | 'adminEmailConfigNoConfig'
  | 'adminEmailConfigIncomplete'
  | 'adminSmtpPortHint'
  | 'adminEmailConfigRequiredFields'
  | 'adminEmailConfigNote'
  | 'adminEmailConfigCurrentSender'
  | 'configureSenderEmailTooltip'
  // Identify Conflicts
  | 'identifyConflictMessage'
  // Excel Template Keys (now templates)
  | 'useExcelTemplate'
  | 'selectTemplate'
  | 'noTemplatesAvailable'
  | 'loadingTemplates'
  | 'excelTemplateFetchError'
  | 'excelTemplateTooltip'
  // Processing retry
  | 'processingRetryAttempt'
  | 'processingFailedAfterRetries'
  // Single Image Page
  | 'extractTableFromImage'
  | 'itemImageTitle'
  | 'nameplateImageTitle'
  | 'singleImageEditTitle'
  | 'singleImagePlaceholder1'
  | 'singleImagePlaceholder2'
  | 'singleImageGenerateButton'
  | 'singleImageGeneratingButton'
  | 'singleImageUploadFirstError'
  | 'singleImageProcessError'
  | 'singleImageInvalidDataError'
  | 'singleImageNoDataError'
  | 'successTitle'
  | 'singleImageExcelSuccessDesc'
  | 'singleImageInvalidExcelApiError'
  | 'editCellTitle'
  | 'editCellTitleDynamic'
  | 'expandCell'
  | 'singleImageClickToUpload'
  | 'singleImageDragAndDrop'
  | 'singleImageFormats'
  | 'singleImageExtractButton'
  | 'singleImageExtractingButton'
  // File Download
  | 'generationSuccessTitle'
  | 'downloadFilesDescription'
  | 'downloadButton'
  | 'closeButton'
  | 'downloadLinkExpiration'
  | 'viewDownloadLinks'
  // Staging
  | 'stageButton'
  | 'stagingData'
  | 'stagedRecordsTitle'
  | 'openStagedRecords'
  | 'stageSuccessTitle'
  | 'stageSuccessDescription'
  | 'stageErrorTitle'
  | 'stageError'
  | 'fetchStagedRecordsError'
  | 'loadStagedRecordError'
  | 'noStagedRecords'
  | 'loadingStagedRecords'
  | 'recordName'
  | 'loadRecord'
  | 'loadRecordSuccess'
  | 'deleteStagedRecordTitle'
  | 'deleteStagedRecordMessage'
  | 'confirmDelete'
  | 'deleteButtonTooltip'
  | 'deleteRecordSuccess'
  | 'stageDeleteError'
  // Camera Options / templates
  | 'templateSelectPlaceholder'
  | 'fetchTemplateError'
  // QR Register Page
  | 'qrRegisterTitle'
  | 'qrRegisterDescription'
  | 'qrRegisterSubmitButton'
  | 'registerAnother'
  | 'printButton'
  | 'qrSameItemWarning'
  // QR Record Page
  | 'qrRecordTitle'
  | 'qrRecordDescription'
  | 'qrUploadPrompt'
  | 'qrUploadSubPrompt'
  | 'qrImagePreviewAlt'
  | 'qrScanning'
  | 'qrScanSuccess'
  | 'qrScanFailedTitle'
  | 'qrScanFailedDescription'
  | 'qrRecordNotFound'
  | 'qrRecordDetailsTitle'
  | 'qrRecordCompleteTitle'
  | 'qrRecordCompleteDescription'
  | 'qrUploadGetUrlError'
  | 'qrUploadToS3Error'
  | 'qrUploadSuccessTitle'
  | 'qrUploadSuccessDescription'
  | 'qrPreviewButton'
  | 'qrPreviewTitle'
  | 'qrPreviewDescription'
  | 'qrPreviewAlt'
  // QR Batch Register Page
  | 'qrBatchRegisterTitle'
  | 'qrBatchRegisterDescription'
  | 'qrBatchUploadPrompt'
  | 'qrBatchSubmitButton'
  | 'qrBatchDownloadTemplate'
  | 'qrBatchInvalidFileType'
  | 'qrBatchNoFileSelected'
  | 'qrBatchUploading'
  | 'qrBatchFileSelected'
  | 'qrBatchCompleteTitle'
  | 'qrBatchSuccessMessage'
  | 'qrBatchDownloadZip'
  // Admin QR Record Management
  | 'adminQrRecordsTitle'
  | 'adminSearchQrRecordsPlaceholder'
  | 'adminLoadingQrRecords'
  | 'adminNoQrRecordsFound'
  | 'adminSerialNumber'
  | 'adminProgress'
  | 'adminUpdatedAt'
  | 'adminViewDetails'
  | 'adminRetakePhoto'
  | 'adminDeleteRecord'
  | 'adminDeleteRecordTitle'
  | 'adminDeleteRecordMessage'
  | 'adminDeleteRecordSuccess'
  | 'adminDeleteRecordError'
  | 'adminQrRecordDetails'
  | 'adminRecordImages'
  | 'adminRecordImage'
  | 'adminClose'
  | 'adminFetchingRecordDetailError'
  | 'adminQrRecordsDescription'
  | 'adminRecentRecordsDescription'
  | 'adminNoImagesFound'
  | 'adminNoImagesFoundMessage'
  | 'adminRetakePhotoSuccess'
  | 'adminRetakePhotoSuccessMessage'
  | 'adminRetakePhotoError'
  | 'adminAccountManagement'
  | 'adminRecordManagement'
  | 'adminRecordManagementOld'
  | 'adminFilterAll'
  | 'adminFilterCompleted'
  | 'adminFilterIncomplete'
  | 'qrScannerInitError'
  | 'clickToRetryInit'
  | 'retryAttempt'
  | 'initializingQrScanner'
  | 'pleaseWait';

export type Translations = Record<TranslationKey, string>;

export type Language = 'en' | 'zh' | 'ko';
