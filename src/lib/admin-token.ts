
'use client';
import type { AdminInfo } from '@/types';

const ADMIN_TOKEN_KEY = 'admin_jwt_token';
const ADMIN_INFO_KEY = 'admin_account_details';

export function saveAdminToken(token: string): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(ADMIN_TOKEN_KEY, token);
    } catch (error) {
      console.error("Error saving admin token to localStorage:", error);
    }
  }
}

export function getAdminToken(): string | null {
  if (typeof window !== 'undefined') {
    try {
      return localStorage.getItem(ADMIN_TOKEN_KEY);
    } catch (error) {
      console.error("Error getting admin token from localStorage:", error);
      return null;
    }
  }
  return null;
}

export function removeAdminToken(): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.removeItem(ADMIN_TOKEN_KEY);
    } catch (error) {
      console.error("Error removing admin token from localStorage:", error);
    }
  }
}

export function saveAdminInfo(adminInfo: AdminInfo): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(ADMIN_INFO_KEY, JSON.stringify(adminInfo));
    } catch (error) {
      console.error("Error saving admin info to localStorage:", error);
    }
  }
}

export function getAdminInfo(): AdminInfo | null {
  if (typeof window !== 'undefined') {
    try {
      const adminInfoString = localStorage.getItem(ADMIN_INFO_KEY);
      return adminInfoString ? JSON.parse(adminInfoString) : null;
    } catch (error) {
      console.error("Error getting admin info from localStorage:", error);
      return null;
    }
  }
  return null;
}

export function removeAdminInfo(): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.removeItem(ADMIN_INFO_KEY);
    } catch (error) {
      console.error("Error removing admin info from localStorage:", error);
    }
  }
}
