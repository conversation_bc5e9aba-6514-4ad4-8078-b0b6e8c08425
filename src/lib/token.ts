
'use client';

const TOKEN_KEY = 'jwt_token';

export function saveToken(token: string): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(TOKEN_KEY, token);
    } catch (error) {
      console.error("Error saving token to localStorage:", error);
    }
  }
}

export function getToken(): string | null {
  if (typeof window !== 'undefined') {
    try {
      return localStorage.getItem(TOKEN_KEY);
    } catch (error) {
      console.error("Error getting token from localStorage:", error);
      return null;
    }
  }
  return null;
}

export function removeToken(): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.removeItem(TOKEN_KEY);
    } catch (error) {
      console.error("Error removing token from localStorage:", error);
    }
  }
}
