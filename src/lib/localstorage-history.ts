
'use client';

/**
 * Saves a value to the history for a given key in localStorage.
 * Ensures the history doesn't exceed the limit and keeps only unique values.
 *
 * @param key - The key for the history list (e.g., 'company', 'region').
 * @param value - The value to add to the history.
 * @param limit - The maximum number of history items to store.
 */
export function saveHistory(key: string, value: string, limit: number): void {
  if (typeof window === 'undefined' || !value) {
    return; // Don't run on server or if value is empty
  }
  try {
    const historyKey = `history_${key}`;
    const existingHistory: string[] = JSON.parse(localStorage.getItem(historyKey) || '[]');

    // Remove the value if it already exists to move it to the front
    const filteredHistory = existingHistory.filter(item => item.toLowerCase() !== value.toLowerCase());

    // Add the new value to the beginning
    const newHistory = [value, ...filteredHistory];

    // Limit the history size
    const limitedHistory = newHistory.slice(0, limit);

    localStorage.setItem(historyKey, JSON.stringify(limitedHistory));
  } catch (error) {
    console.error(`Error saving history for key "${key}" to localStorage:`, error);
  }
}

/**
 * Retrieves the history for a given key from localStorage.
 *
 * @param key - The key for the history list (e.g., 'company', 'region').
 * @param limit - The maximum number of items to retrieve (should match the limit used for saving).
 * @returns An array of history strings, or an empty array if none found or on error.
 */
export function getHistory(key: string, limit: number): string[] {
  if (typeof window === 'undefined') {
    return []; // Don't run on server
  }
  try {
    const historyKey = `history_${key}`;
    const history: string[] = JSON.parse(localStorage.getItem(historyKey) || '[]');
    // Ensure the retrieved history doesn't exceed the limit, just in case
    return history.slice(0, limit);
  } catch (error) {
    console.error(`Error retrieving history for key "${key}" from localStorage:`, error);
    return [];
  }
}
