{"appName": "이미지 입력 (구)", "headerInfo": "헤더 정보", "company": "회사", "area": "지역", "item": "항목", "images": "이미지", "uploadImages": "이미지 업로드", "uploadDisabled": "이전 이미지 먼저 업로드", "maxImages": "최대 {maxImages}개의 이미지만 허용됩니다.", "addMore": "이미지 더 추가하기", "photoNumber": "사진 번호", "imagePreviewAlt": "이미지 미리보기", "status": "상태", "submit": "제출 및 Excel 생성", "submitting": "제출 중...", "submitSuccess": "제출 성공. 파일이 준비되었습니다.", "submitSuccessAndSent": "생성 및 전송됨", "submitError": "Excel 파일 생성 중 오류가 발생했습니다. 다시 시도하십시오.", "uploading": "처리 중...", "processingImages": "이미지 처리 중...", "processImages": "이미지 처리", "processingNeeded": "일부 이미지는 처리가 필요합니다 (예: 순서 변경 또는 이전 오류). 제출하기 전에 '이미지 처리'를 다시 클릭하십시오.", "uploadError": "이미지 처리 중 오류 발생:", "selectOption": "옵션을 선택하세요", "selectOrEnter": "선택 또는 입력...", "orEnterValue": "또는 값을 입력하세요", "searchOrEnter": "검색 또는 값 입력...", "removeImage": "이미지 제거", "removeSlot": "슬롯 제거", "changeLanguage": "언어", "english": "영어", "chinese": "중국어", "korean": "한국어", "languageNameEn": "영어", "languageNameZh": "중국어 (간체)", "languageNameKo": "한국어", "optionsLoadError": "양식 옵션을 로드하지 못했습니다.", "errorTitle": "오류", "optionsLoadErrorDescription": "초기 양식 옵션을 로드하지 못했습니다.", "noImagesToProcessTitle": "처리할 이미지 없음", "noImagesToProcessDescription": "이미지를 하나 이상 업로드하거나 이미지가 처리되어야 하는지 확인하십시오.", "processingCompleteTitle": "이미지 처리 완료", "processingCompleteDescription": "추출된 데이터 및 장치 상태를 검토하십시오. 순서를 변경하면 다시 처리해야 합니다.", "batchProcessingErrorTitle": "일괄 처리 오류", "cannotRemoveLastSlotTitle": "마지막 슬롯을 제거할 수 없음", "cannotRemoveLastSlotDescription": "최소 {minImages}개의 이미지 슬롯이 남아 있어야 합니다.", "invalidMoveTitle": "잘못된 이동", "invalidMoveDescription": "이미지를 이동하여 공백을 만들 수 없습니다. 마지막으로 채워진 이미지 바로 뒤에 배치하거나 다른 이미지와 교체하십시오.", "orderChangedTitle": "이미지 순서 변경됨", "orderChangedDescription": "올바른 데이터 연결을 위해 이미지를 다시 처리하십시오.", "missingInfoTitle": "정보 누락", "missingInfoDescription": "모든 필수 필드를 입력하십시오.", "processingRequiredTitle": "처리 필요", "waitForProcessingDescription": "모든 이미지 처리가 완료될 때까지 기다리십시오.", "missingValidImagesTitle": "유효한 이미지 누락", "missingValidImagesDescription": "이미지를 하나 이상 업로드하고 성공적으로 처리하거나 업로드된 이미지에 오류가 없는지 확인하십시오.", "missingStatusTitle": "상태 누락", "missingStatusDescription": "처리된 모든 이미지에 상태가 선택되거나 추출되었는지 확인하십시오. 이미지 확인: {imageNumbers}", "imageErrorsTitle": "이미지 오류", "imageErrorsDescription": "오류를 수정하거나 이미지 제거: {imageNumbers}", "notApplicable": "해당 없음", "comboboxInputLabel": "콤보박스 입력", "noResultsFound": "결과 없음.", "dragToReorder": "끌어서 순서 변경", "cannotDragEmptySlot": "빈 슬롯은 끌 수 없습니다. 먼저 이미지를 업로드하십시오.", "loading": "로드 중...", "retry": "재시도", "notAssigned": "할당되지 않음", "noStatusFound": "상태 없음.", "statusInputLabel": "상태 선택 또는 입력", "invalidApiResponse": "이미지 처리 API의 응답 형식이 잘못되었습니다.", "apiNoResultError": "API에서 이 이미지에 대한 결과를 반환하지 않았습니다.", "unknownProcessingError": "알 수 없는 처리 오류.", "unknownBatchError": "알 수 없는 일괄 처리 오류.", "invalidExcelApiResponse": "Excel 생성 API의 응답이 잘못되었습니다 (파일 배열 누락).", "loginTitle": "로그인", "loginDescription": "애플리케이션에 액세스하려면 자격 증명을 입력하십시오.", "username": "사용자 이름", "password": "비밀번호", "loginButton": "로그인", "logoutButton": "로그아웃", "loginErrorTitle": "로그인 실패", "loginErrorInvalid": "잘못된 사용자 이름 또는 비밀번호입니다.", "loginErrorMissingToken": "로그인 실패: 서버에서 토큰을 받지 못했습니다.", "loginErrorNetwork": "로그인 실패: 네트워크 오류입니다. 다시 시도하십시오.", "switchToAdminLogin": "관리자 로그인", "switchToUserLogin": "사용자 로그인", "sessionExpiredTitle": "세션 만료됨", "sessionExpiredDescription": "세션이 만료되었습니다. 다시 로그인하십시오.", "networkErrorTitle": "네트워크 오류", "networkErrorDescription": "네트워크 오류가 발생했습니다. 연결을 확인하고 다시 시도하십시오.", "networkErrorOnVerify": "네트워크 오류로 인해 세션을 확인할 수 없습니다. 연결을 확인하십시오.", "verifyingUserInfoTitle": "세션 확인 중", "verifyingUserInfoDescription": "사용자 정보를 확인하는 동안 잠시 기다려 주십시오...", "currentLocationTitle": "위치 정보", "refreshLocation": "위치 새로고침", "fetchingLocation": "위치 가져오는 중...", "latitude": "위도", "longitude": "경도", "address": "주소", "fetchingAddress": "주소 가져오는 중...", "addressNotFound": "이 좌표에 대한 주소를 찾을 수 없습니다.", "addressNotAvailable": "주소를 사용할 수 없습니다.", "geocodingApiKeyMissing": "지오코딩을 위한 Google Maps API 키가 없습니다.", "geocodingApiKeyMissingFull": "지오코딩을 위한 Google Maps API 키가 없습니다. 주소 조회가 비활성화되었습니다. NEXT_PUBLIC_GOOGLE_MAPS_API_KEY를 구성하십시오.", "geocodingError": "Google Maps에서 주소를 가져오는 중 오류 발생.", "geolocationNotSupported": "브라우저에서 지오로케이션을 지원하지 않습니다.", "geolocationPermissionDenied": "지오로케이션 권한이 거부되었습니다. 브라우저 설정에서 이 사이트의 위치 서비스를 활성화하십시오.", "geolocationPositionUnavailable": "위치 정보를 사용할 수 없습니다.", "geolocationTimeout": "사용자 위치를 가져오는 요청 시간이 초과되었습니다.", "geolocationUnknownError": "위치를 가져오는 동안 알 수 없는 오류가 발생했습니다.", "locationErrorTitle": "위치 오류", "addressInputPlaceholder": "주소를 입력하거나 가져온 위치 사용", "addressHistoryEmpty": "최근 주소 없음", "latitudeLabel": "위도", "longitudeLabel": "경도", "adminLoginTitle": "관리자 로그인", "adminLoginDescription": "사용자 및 시스템 설정을 관리하려면 로그인하십시오.", "adminLoginButton": "관리자 로그인", "adminLoginButtonTitle": "관리자 패널", "adminDashboardTitle": "관리자 대시보드", "adminUserManagement": "사용자 관리", "adminLogoutButton": "관리자 로그아웃", "adminAddUser": "사용자 추가", "adminEditUser": "사용자 수정", "adminEmail": "이메일", "adminActions": "작업", "adminStatus": "상태", "adminStatusActive": "활성", "adminStatusDisabled": "비활성", "adminCreatedAt": "생성 날짜", "adminConfirmPassword": "비밀번호 확인", "adminPasswordMismatch": "비밀번호가 일치하지 않습니다.", "adminPasswordMinLengthError": "비밀번호는 8자 이상이어야 합니다.", "adminPasswordNumericOnlyError": "비밀번호는 숫자로만 구성될 수 없습니다.", "adminAddUserSuccess": "최종 사용자가 성공적으로 생성되었습니다.", "adminAddUserError": "사용자 추가에 실패했습니다.", "adminUpdateEmailSuccess": "이메일이 성공적으로 업데이트되었습니다.", "adminUpdateEmailError": "이메일 업데이트에 실패했습니다.", "adminUpdatePasswordSuccess": "비밀번호가 성공적으로 업데이트되었습니다.", "adminUpdatePasswordError": "비밀번호 업데이트에 실패했습니다.", "adminUpdateStatusSuccess": "사용자 상태가 성공적으로 업데이트되었습니다.", "adminUpdateStatusError": "사용자 상태 업데이트에 실패했습니다.", "adminUserNotFoundError": "사용자를 찾을 수 없거나 권한이 없습니다.", "adminEmailExistsError": "이미 사용 중인 이메일입니다.", "adminUsernameExistsError": "이미 사용 중인 사용자 이름입니다.", "adminQuotaExceededError": "하위 계정 할당량이 초과되었습니다.", "adminGenericError": "예상치 못한 오류가 발생했습니다.", "adminAccountExpiredError": "관리자 계정이 만료되었습니다.", "adminInvalidCredentialsError": "잘못된 관리자 자격 증명입니다.", "adminAuthRequiredError": "관리자 인증이 필요합니다. 다시 로그인하십시오.", "adminUserSummaryTitle": "사용자 계정 요약", "adminTotalUsers": "총 사용자 수", "adminActiveUsers": "활성 사용자 수", "adminDisabledUsers": "비활성 사용자 수", "adminQuotaLimit": "할당량 한도", "adminQuotaUsed": "사용된 할당량", "adminQuotaRemaining": "남은 할당량", "adminConfirmDisableUserTitle": "사용자 비활성화 확인", "adminConfirmEnableUserTitle": "사용자 활성화 확인", "adminConfirmDisableUserMessage": "{username} 사용자를 비활성화하시겠습니까?", "adminConfirmEnableUserMessage": "{username} 사용자를 활성화하시겠습니까?", "adminCancel": "취소", "adminConfirm": "확인", "adminEditEmailTitle": "이메일 수정", "adminEditEmailMultipleHint": "하나 이상의 이메일 주소를 쉼표로 구분하여 입력하십시오.", "adminMaxEmailsError": "최대 5개의 이메일 주소만 입력할 수 있습니다.", "adminEditPasswordTitle": "비밀번호 변경", "adminNewPassword": "새 비밀번호", "adminSaveChanges": "변경 사항 저장", "adminSaving": "저장 중...", "adminUsersList": "최종 사용자 목록", "adminEnableUser": "사용자 활성화", "adminDisableUser": "사용자 비활성화", "adminLoadingUsers": "사용자 로드 중...", "adminNoUsersFound": "최종 사용자를 찾을 수 없습니다.", "adminNoActiveUsersFound": "활성 사용자를 찾을 수 없습니다. 비활성화된 사용자 표시를 시도해 보세요.", "adminFetchingUsersError": "사용자를 가져오는 데 실패했습니다.", "adminAccountDetailsTitle": "관리자 계정 정보", "adminOrganization": "조직", "adminAccountExpireTime": "계정 만료 시간", "adminAccountStatus": "계정 상태", "adminStatusExpired": "만료됨", "adminStatusNotExpired": "활성", "adminAccountInfoNotAvailable": "관리자 계정 정보를 사용할 수 없습니다.", "adminShowDisabledUsers": "비활성화된 계정 표시", "adminRemainingDays": "(잔여 {days}일)", "adminRecentRecordsTitle": "최근 30일 기록", "adminLoadingRecords": "기록 로드 중...", "adminNoRecordsFound": "선택한 필터에 대한 기록을 찾을 수 없습니다.", "adminFetchingRecordsError": "기록을 가져오는 데 실패했습니다.", "adminRecordName": "기록 이름", "adminDownloadError": "다운로드 링크를 가져오는 데 실패했습니다.", "adminRecordNotFound": "기록을 찾을 수 없습니다.", "adminSearchRecordsPlaceholder": "사용자 이름 또는 기록 이름으로 검색...", "clearFormButton": "양식 지우기", "formClearedToastTitle": "양식 지워짐", "formClearedToastDescription": "모든 입력이 기본 상태로 재설정되었습니다.", "adminSenderEmailLabel": "발신자 이메일", "adminSystemDefaultSender": "시스템 기본값", "adminConfigureEmailTitle": "이메일 설정 구성", "adminEnableCustomEmailConfig": "사용자 지정 이메일 구성 사용", "adminSenderPasswordLabel": "발신자 비밀번호 또는 앱 비밀번호", "adminSmtpHostLabel": "SMTP 호스트", "adminSmtpPortLabel": "SMTP 포트", "adminSaveEmailConfigButton": "구성 저장", "adminTestEmailConfigButton": "구성 테스트", "adminTestingEmailConfig": "테스트 중...", "adminSavingEmailConfig": "저장 중...", "adminEmailConfigFetchError": "이메일 구성을 가져오지 못했습니다.", "adminEmailConfigSaveSuccess": "이메일 구성이 성공적으로 저장되었습니다.", "adminEmailConfigSaveError": "이메일 구성을 저장하지 못했습니다.", "adminEmailConfigTestSuccess": "{time}에 {recipient}에게 테스트 이메일이 전송되었습니다.", "adminEmailConfigTestError": "테스트 이메일 전송 실패: {details}", "adminEmailConfigTestErrorGeneric": "테스트 이메일 전송에 실패했습니다.", "adminEmailConfigDisabled": "사용자 지정 이메일 구성이 비활성화되었습니다.", "adminEmailConfigNoConfig": "사용자 지정 이메일 구성이 없습니다.", "adminEmailConfigIncomplete": "이메일 구성이 완료되지 않았습니다. 활성화하는 경우 모든 필드를 채우십시오.", "adminSmtpPortHint": "예: TLS의 경우 587, SSL의 경우 465", "adminEmailConfigRequiredFields": "사용자 지정 구성을 활성화할 때 발신자 이메일, 비밀번호, SMTP 호스트 및 포트가 필요합니다.", "adminEmailConfigNote": "참고: 활성화하는 경우 모든 필드(이메일, 비밀번호, 호스트, 포트)를 채워야 합니다. 비밀번호는 보기를 위해 저장되지 않으며 활성화된 구성을 변경하는 경우 다시 입력해야 합니다.", "adminEmailConfigCurrentSender": "현재 발신자", "configureSenderEmailTooltip": "발신자 이메일 구성", "identifyConflictMessage": "식별 충돌. 확인 후 올바른 값을 수동으로 입력해 주십시오.", "useExcelTemplate": "Excel 템플릿 사용", "selectTemplate": "템플릿 선택", "noTemplatesAvailable": "사용 가능한 템플릿 없음", "loadingTemplates": "템플릿 로드 중...", "excelTemplateFetchError": "Excel 템플릿을 가져오지 못했습니다.", "submitSuccessMessage": "성공!", "processingRetryAttempt": "처리 실패. 재시도 중... (시도 {currentAttempt}/{maxAttempts})", "processingFailedAfterRetries": "여러 번 시도 후 처리 실패. 연결을 확인하고 다시 시도하십시오.", "extractTableFromImage": "명판 인식", "itemImageTitle": "항목 이미지", "nameplateImageTitle": "명판 이미지", "singleImageEditTitle": "표 편집 및 다운로드", "singleImagePlaceholder1": "추출된 표 데이터가 여기에 표시됩니다.", "singleImagePlaceholder2": "이미지를 업로드하고 '표 추출'을 클릭하십시오.", "singleImageGenerateButton": "Excel 생성", "singleImageGeneratingButton": "생성 중...", "singleImageUploadFirstError": "먼저 두 이미지를 모두 업로드하십시오.", "singleImageProcessError": "이미지 처리에 실패했습니다.", "singleImageInvalidDataError": "서버에서 잘못된 데이터 형식을 받았습니다.", "singleImageNoDataError": "제출할 표 데이터나 이미지가 없습니다.", "successTitle": "성공", "singleImageExcelSuccessDesc": "Excel 파일이 생성되어 다운로드 중입니다.", "singleImageInvalidExcelApiError": "잘못된 Excel API 응답입니다.", "excelTemplateTooltip": "여러 템플릿을 선택하면 하나의 파일만 로컬에 다운로드됩니다. 추가 파일은 이메일을 확인하십시오.", "editCellTitle": "셀 편집", "editCellTitleDynamic": "셀 편집 (행 {row}, 열 {col})", "expandCell": "확대하여 편집", "singleImageClickToUpload": "클릭하여 업로드", "singleImageDragAndDrop": "또는 드래그 앤 드롭", "singleImageFormats": "PNG, JPG 등", "singleImageExtractButton": "표 추출", "singleImageExtractingButton": "추출 중...", "generationSuccessTitle": "다운로드 준비 완료", "downloadFilesDescription": "생성된 파일은 아래에 나열되어 있습니다.", "downloadButton": "다운로드", "closeButton": "닫기", "downloadLinkExpiration": "참고: 다운로드 링크는 30분 동안 유효합니다.", "viewDownloadLinks": "다운로드 링크 보기", "stageButton": "임시 저장", "stagingData": "임시 저장 중...", "stagedRecordsTitle": "임시 저장된 기록", "openStagedRecords": "임시 저장 기록 열기", "stageSuccessTitle": "임시 저장 성공", "stageSuccessDescription": "데이터가 성공적으로 임시 저장되었으며, 양식이 비워졌습니다.", "stageErrorTitle": "임시 저장 실패", "stageError": "데이터 임시 저장에 실패했습니다.", "fetchStagedRecordsError": "임시 저장된 기록을 가져오는 데 실패했습니다.", "loadStagedRecordError": "임시 저장된 기록을 불러오는 데 실패했습니다.", "noStagedRecords": "임시 저장된 기록이 없습니다.", "loadingStagedRecords": "임시 저장된 기록을 불러오는 중입니다...", "recordName": "기록 이름", "loadRecord": "로드", "loadRecordSuccess": "임시 저장된 기록을 성공적으로 불러왔습니다.", "deleteStagedRecordTitle": "삭제 확인", "deleteStagedRecordMessage": "'{recordName}' 임시 저장 기록을 영구적으로 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "confirmDelete": "삭제", "deleteButtonTooltip": "임시 저장 기록 삭제", "deleteRecordSuccess": "기록이 성공적으로 삭제되었습니다.", "stageDeleteError": "임시 저장된 기록을 삭제하지 못했습니다.", "templateSelectPlaceholder": "템플릿 선택...", "fetchTemplateError": "템플릿을 가져오는 데 실패했습니다.", "qrRegisterTitle": "새 시리얼 번호 등록", "qrRegisterDescription": "새 QR 코드를 생성하려면 양식을 작성하십시오.", "qrRegisterSubmitButton": "QR 코드 생성", "registerAnother": "다른 항목 등록", "printButton": "인쇄", "qrRecordTitle": "이미지 입력", "qrRecordDescription": "QR 코드가 있는 이미지를 업로드하여 기록을 가져오고 업데이트하십시오.", "qrUploadPrompt": "이미지를 업로드하려면 클릭하십시오", "qrUploadSubPrompt": "QR 코드가 자동으로 스캔됩니다.", "qrImagePreviewAlt": "QR 스캔을 위해 업로드된 이미지 미리보기", "qrScanning": "QR 코드 스캔 중...", "qrScanSuccess": "QR 코드 스캔 성공!", "qrScanFailedTitle": "스캔 실패", "qrScanFailedDescription": "이미지에서 QR 코드를 감지할 수 없습니다. 다른 이미지를 시도하십시오.", "qrRecordNotFound": "기록을 찾을 수 없습니다", "qrUploadToS3Error": "S3에 이미지 업로드 실패", "qrUploadSuccessTitle": "업로드 성공", "qrUploadSuccessDescription": "새 이미지가 기록에 추가되었습니다.", "qrPreviewButton": "미리보기", "qrPreviewTitle": "Excel 미리보기", "qrPreviewDescription": "SN: {sn}에 대해 생성된 Excel 파일의 미리보기입니다.", "qrPreviewAlt": "{sn}에 대해 생성된 Excel 미리보기", "qrBatchRegisterTitle": "일괄 등록", "qrBatchRegisterDescription": "Excel 파일을 업로드하여 여러 기록을 한 번에 등록합니다.", "qrBatchUploadPrompt": "여기에 Excel 파일을 클릭하거나 드래그 앤 드롭하여 업로드하세요.", "qrBatchSubmitButton": "업로드 및 생성", "qrBatchDownloadTemplate": "템플릿 다운로드", "qrBatchInvalidFileType": "잘못된 파일 형식입니다. .xlsx 또는 .xls 파일을 업로드하세요.", "qrBatchNoFileSelected": "업로드할 파일을 선택하세요.", "qrBatchUploading": "업로드 및 처리 중...", "qrBatchFileSelected": "파일이 선택되었습니다. 업로드 준비 완료.", "qrBatchCompleteTitle": "일괄 등록 완료", "qrBatchSuccessMessage": "{count}개의 기록이 성공적으로 처리되었습니다.", "qrBatchDownloadZip": "ZIP 패키지 다운로드", "qrSameItemWarning": "동일한 항목을 반복적으로 제출하면 동일한 일련 번호가 생성됩니다.", "clickToStartCapture": "여기를 클릭하여 촬영 시작", "realTimeScanAndCapture": "실시간 QR 스캔 및 사진 촬영", "stopCapture": "촬영 중지", "retakePhoto": "다시 촬영", "nameplateEditorTitle": "네임플레이트 편집: {sn}", "nameplateDataGridTitle": "명판 내용", "loadingNameplate": "네임플레이트 데이터 로드 중...", "nameplateKeyHeader": "항목", "adminQrRecordsTitle": "QR 기록 관리", "adminSearchQrRecordsPlaceholder": "시리얼 번호, 회사, 지역, 항목, 템플릿 또는 사용자 이름으로 검색...", "adminLoadingQrRecords": "QR 기록 로드 중...", "adminNoQrRecordsFound": "QR 기록을 찾을 수 없습니다.", "adminSerialNumber": "시리얼 번호", "adminProgress": "진행률", "adminUpdatedAt": "업데이트 시간", "adminViewDetails": "세부 정보 보기", "adminRetakePhoto": "다시 촬영", "adminDeleteRecord": "기록 삭제", "adminDeleteRecordTitle": "QR 기록 삭제", "adminDeleteRecordMessage": "시리얼 번호 {serialNumber}의 기록을 영구적으로 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "adminDeleteRecordSuccess": "QR 기록이 성공적으로 삭제되었습니다.", "adminDeleteRecordError": "QR 기록 삭제에 실패했습니다.", "adminQrRecordDetails": "QR 기록 세부 정보", "adminRecordImages": "기록 이미지", "adminRecordImage": "기록 이미지", "adminClose": "닫기", "adminFetchingRecordDetailError": "기록 세부 정보를 가져오는 데 실패했습니다.", "adminQrRecordsDescription": "QR 코드 기록을 관리하고, 세부 정보를 보고, 다시 촬영을 처리합니다.", "adminRecentRecordsDescription": "모든 사용자의 최근 30일 기록을 보고 관리합니다.", "adminNoImagesFound": "이미지를 찾을 수 없음", "adminNoImagesFoundMessage": "이 기록에 사용할 수 있는 이미지가 없습니다.", "adminRetakePhotoSuccess": "기록 재설정 성공", "adminRetakePhotoSuccessMessage": "기록이 재설정되어 새로운 사진을 찍을 수 있습니다.", "adminRetakePhotoError": "사진 재촬영을 위한 기록 재설정에 실패했습니다.", "adminAccountManagement": "계정 관리", "adminRecordManagement": "기록 관리", "adminRecordManagementOld": "기록 관리 (구)", "adminFilterAll": "모든 기록", "adminFilterCompleted": "완료됨", "adminFilterIncomplete": "미완료", "qrScannerInitError": "QR 스캐너 초기화 실패", "clickToRetryInit": "초기화 재시도하려면 클릭", "retryAttempt": "재시도 횟수", "initializingQrScanner": "QR 스캐너 초기화 중...", "pleaseWait": "잠시 기다려 주세요"}