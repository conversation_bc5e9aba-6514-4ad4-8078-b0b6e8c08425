{"appName": "Image Entry (Old)", "headerInfo": "Header Information", "company": "Company", "area": "Area", "item": "<PERSON><PERSON>", "images": "Images", "uploadImages": "Upload Images", "uploadDisabled": "Upload Previous First", "maxImages": "Maximum {maxImages} images allowed.", "addMore": "Add More Images", "photoNumber": "Photo Number", "imagePreviewAlt": "Image preview", "status": "Status", "submit": "Submit", "submitting": "Submitting...", "submitSuccess": "Submission successful. Your files are ready below.", "submitSuccessMessage": "Success!", "submitSuccessAndSent": "Generated & Sent", "submitError": "Error generating Excel file. Please try again.", "uploading": "Processing...", "processingImages": "Processing Images...", "processImages": "Process Images", "processingNeeded": "Some images need processing (e.g., due to reordering or previous error). Click 'Process Images' again before submitting.", "uploadError": "Error processing image:", "selectOption": "Select an option", "selectOrEnter": "Select or enter...", "orEnterValue": "or enter a value", "searchOrEnter": "Search or enter value...", "removeImage": "Remove Image", "removeSlot": "Remove Slot", "changeLanguage": "Language", "english": "English", "chinese": "Chinese", "korean": "Korean", "languageNameEn": "English", "languageNameZh": "Simplified Chinese", "languageNameKo": "Korean", "optionsLoadError": "Failed to load form options.", "errorTitle": "An Error Occurred", "optionsLoadErrorDescription": "Failed to load initial form options.", "noImagesToProcessTitle": "No images to process", "noImagesToProcessDescription": "Please upload at least one image, or ensure images need processing.", "processingCompleteTitle": "Image processing complete", "processingCompleteDescription": "Review extracted data and device statuses. Any reordering will require reprocessing.", "batchProcessingErrorTitle": "Batch Processing Error", "cannotRemoveLastSlotTitle": "Cannot remove last slot", "cannotRemoveLastSlotDescription": "At least {minImages} image slot must remain.", "invalidMoveTitle": "Invalid Move", "invalidMoveDescription": "Cannot move an image to create a gap. Place it directly after the last filled image or swap with another image.", "orderChangedTitle": "Image order changed", "orderChangedDescription": "Please process images again to ensure correct data association.", "missingInfoTitle": "Missing Information", "missingInfoDescription": "Please fill in all required fields.", "processingRequiredTitle": "Processing Required", "waitForProcessingDescription": "Please wait for all images to finish processing.", "missingValidImagesTitle": "Missing Valid <PERSON>", "missingValidImagesDescription": "Please upload and successfully process at least one image, or ensure uploaded images don't have errors.", "missingStatusTitle": "Missing Status", "missingStatusDescription": "Please ensure all processed images have a Status selected or extracted. Check image(s): {imageNumbers}", "imageErrorsTitle": "Image Errors", "imageErrorsDescription": "Please fix errors or remove image(s): {imageNumbers}", "notApplicable": "N/A", "comboboxInputLabel": "Combobox input", "noResultsFound": "No results found.", "dragToReorder": "Drag to reorder", "cannotDragEmptySlot": "Cannot drag an empty slot. Upload an image first.", "loading": "Loading...", "retry": "Retry", "notAssigned": "Not assigned", "noStatusFound": "No status found.", "statusInputLabel": "Status selection or input", "invalidApiResponse": "Invalid response format from image processing API.", "apiNoResultError": "No result returned from API for this image.", "unknownProcessingError": "Unknown processing error.", "unknownBatchError": "Unknown batch processing error.", "invalidExcelApiResponse": "Invalid response from Excel generation API (missing files array).", "loginTitle": "<PERSON><PERSON>", "loginDescription": "Enter your credentials to access the application.", "username": "Username", "password": "Password", "loginButton": "<PERSON><PERSON>", "logoutButton": "Logout", "loginErrorTitle": "Login Failed", "loginErrorInvalid": "Invalid username or password.", "loginErrorMissingToken": "<PERSON><PERSON> failed: No token received from server.", "loginErrorNetwork": "<PERSON><PERSON> failed: Network error. Please try again.", "switchToAdminLogin": "<PERSON><PERSON>", "switchToUserLogin": "User Login", "sessionExpiredTitle": "Session Expired", "sessionExpiredDescription": "Your session has expired. Please login again.", "networkErrorTitle": "Network Error", "networkErrorDescription": "A network error occurred. Please check your connection and try again.", "networkErrorOnVerify": "Could not verify session due to a network error. Please check your connection.", "verifyingUserInfoTitle": "Verifying Session", "verifyingUserInfoDescription": "Please wait while we confirm your user information...", "currentLocationTitle": "Location Information", "refreshLocation": "Refresh Location", "fetchingLocation": "Fetching location...", "latitude": "Latitude", "longitude": "Longitude", "address": "Address", "fetchingAddress": "Fetching address...", "addressNotFound": "Address not found for these coordinates.", "addressNotAvailable": "Address not available.", "geocodingApiKeyMissing": "Google Maps API Key for geocoding is missing.", "geocodingApiKeyMissingFull": "Google Maps API Key for geocoding is missing. Address lookup is disabled. Please configure NEXT_PUBLIC_GOOGLE_MAPS_API_KEY.", "geocodingError": "Error fetching address from Google Maps.", "geolocationNotSupported": "Geolocation is not supported by your browser.", "geolocationPermissionDenied": "Geolocation permission denied. Please enable location services for this site in your browser settings.", "geolocationPositionUnavailable": "Location information is unavailable.", "geolocationTimeout": "The request to get user location timed out.", "geolocationUnknownError": "An unknown error occurred while fetching location.", "locationErrorTitle": "Location Error", "addressInputPlaceholder": "Enter address or use fetched location", "addressHistoryEmpty": "No recent addresses", "latitudeLabel": "Lat", "longitudeLabel": "Lon", "adminLoginTitle": "<PERSON><PERSON>", "adminLoginDescription": "Login to manage users and system settings.", "adminLoginButton": "<PERSON><PERSON>", "adminLoginButtonTitle": "Admin Panel", "adminDashboardTitle": "Admin Dashboard", "adminUserManagement": "User Management", "adminLogoutButton": "<PERSON><PERSON>", "adminAddUser": "Add User", "adminEditUser": "Edit User", "adminEmail": "Email", "adminActions": "Actions", "adminStatus": "Status", "adminStatusActive": "Active", "adminStatusDisabled": "Disabled", "adminCreatedAt": "Created At", "adminConfirmPassword": "Confirm Password", "adminPasswordMismatch": "Passwords do not match.", "adminPasswordMinLengthError": "Password must be at least 8 characters long.", "adminPasswordNumericOnlyError": "Password cannot be purely numeric.", "adminAddUserSuccess": "End user created successfully.", "adminAddUserError": "Failed to add user.", "adminUpdateEmailSuccess": "Email updated successfully.", "adminUpdateEmailError": "Failed to update email.", "adminUpdatePasswordSuccess": "Password updated successfully.", "adminUpdatePasswordError": "Failed to update password.", "adminUpdateStatusSuccess": "User status updated successfully.", "adminUpdateStatusError": "Failed to update user status.", "adminUserNotFoundError": "User not found or not authorized.", "adminEmailExistsError": "Email already exists.", "adminUsernameExistsError": "Username already exists.", "adminQuotaExceededError": "Sub-account quota exceeded.", "adminGenericError": "An unexpected error occurred.", "adminAccountExpiredError": "Admin account has expired.", "adminInvalidCredentialsError": "Invalid admin credentials.", "adminAuthRequiredError": "Admin authentication required. Please login again.", "adminUserSummaryTitle": "User Account Summary", "adminTotalUsers": "Total Users", "adminActiveUsers": "Active Users", "adminDisabledUsers": "Disabled Users", "adminQuotaLimit": "<PERSON><PERSON><PERSON>", "adminQuotaUsed": "Quota Used", "adminQuotaRemaining": "<PERSON><PERSON><PERSON>", "adminConfirmDisableUserTitle": "Confirm Disable User", "adminConfirmEnableUserTitle": "Confirm Enable User", "adminConfirmDisableUserMessage": "Are you sure you want to disable user {username}?", "adminConfirmEnableUserMessage": "Are you sure you want to enable user {username}?", "adminCancel": "Cancel", "adminConfirm": "Confirm", "adminEditEmailTitle": "<PERSON> Email", "adminEditEmailMultipleHint": "Enter one or more email addresses, separated by commas.", "adminMaxEmailsError": "You can enter a maximum of 5 email addresses.", "adminEditPasswordTitle": "Change Password", "adminNewPassword": "New Password", "adminSaveChanges": "Save Changes", "adminSaving": "Saving...", "adminUsersList": "End Users List", "adminEnableUser": "Enable User", "adminDisableUser": "Disable User", "adminLoadingUsers": "Loading users...", "adminNoUsersFound": "No end users found.", "adminNoActiveUsersFound": "No active users found. Try showing disabled users.", "adminFetchingUsersError": "Failed to fetch users.", "adminAccountDetailsTitle": "Admin Account Details", "adminOrganization": "Organization", "adminAccountExpireTime": "Account Expires", "adminAccountStatus": "Account Status", "adminStatusExpired": "Expired", "adminStatusNotExpired": "Active", "adminAccountInfoNotAvailable": "Admin account details are not available.", "adminShowDisabledUsers": "Show Disabled Users", "adminRemainingDays": "(Remaining {days} days)", "adminRecentRecordsTitle": "Recent 30 Days Records", "adminLoadingRecords": "Loading records...", "adminNoRecordsFound": "No records found for the selected filter.", "adminFetchingRecordsError": "Failed to fetch records.", "adminRecordName": "Record Name", "adminDownloadError": "Failed to get download link.", "adminRecordNotFound": "Record not found.", "adminSearchRecordsPlaceholder": "Search by username or record name...", "clearFormButton": "Clear Form", "formClearedToastTitle": "Form Cleared", "formClearedToastDescription": "All inputs have been reset to their default state.", "adminSenderEmailLabel": "Sender <PERSON><PERSON>", "adminSystemDefaultSender": "System Default", "adminConfigureEmailTitle": "Configure <PERSON><PERSON>", "adminEnableCustomEmailConfig": "Enable Custom Email Configuration", "adminSenderPasswordLabel": "Sender Password or App Password", "adminSmtpHostLabel": "SMTP Host", "adminSmtpPortLabel": "SMTP Port", "adminSaveEmailConfigButton": "Save Configuration", "adminTestEmailConfigButton": "Test Configuration", "adminTestingEmailConfig": "Testing...", "adminSavingEmailConfig": "Saving...", "adminEmailConfigFetchError": "Failed to fetch email configuration.", "adminEmailConfigSaveSuccess": "Email configuration saved successfully.", "adminEmailConfigSaveError": "Failed to save email configuration.", "adminEmailConfigTestSuccess": "Test email sent to {recipient} at {time}.", "adminEmailConfigTestError": "Failed to send test email: {details}", "adminEmailConfigTestErrorGeneric": "Failed to send test email.", "adminEmailConfigDisabled": "Custom email configuration is disabled.", "adminEmailConfigNoConfig": "No custom email configuration found.", "adminEmailConfigIncomplete": "Email configuration is incomplete. Please fill all fields if enabling.", "adminSmtpPortHint": "E.g., 587 for TLS, 465 for SSL", "adminEmailConfigRequiredFields": "Sender Email, Password, SMTP Host, and Port are required when enabling custom configuration.", "adminEmailConfigNote": "Note: If enabling, all fields (Email, Password, Host, Port) must be filled. Password is not stored for viewing and must be re-entered if making changes to an enabled configuration.", "adminEmailConfigCurrentSender": "Current Sender", "configureSenderEmailTooltip": "Configure Sender <PERSON>ail", "identifyConflictMessage": "Identification conflict. Please verify and manually enter the correct value.", "useExcelTemplate": "Use Excel Template", "selectTemplate": "Select Template", "noTemplatesAvailable": "No templates available", "loadingTemplates": "Loading templates...", "excelTemplateFetchError": "Failed to fetch Excel templates.", "excelTemplateTooltip": "When multiple templates are selected, only one file will be downloaded locally. Please check your email for additional files.", "processingRetryAttempt": "Processing failed. Retrying... (Attempt {currentAttempt}/{maxAttempts})", "processingFailedAfterRetries": "Processing failed after multiple attempts. Please check your connection and try again.", "extractTableFromImage": "Nameplate Recognition", "itemImageTitle": "Item Image", "nameplateImageTitle": "Nameplate Image", "singleImageEditTitle": "Edit Table & Download", "singleImagePlaceholder1": "Extracted table data will appear here.", "singleImagePlaceholder2": "Upload an image and click \"Extract Table\".", "singleImageGenerateButton": "Generate Excel", "singleImageGeneratingButton": "Generating...", "singleImageUploadFirstError": "Please upload both images first.", "singleImageProcessError": "Failed to process image.", "singleImageInvalidDataError": "Invalid data format received from server.", "singleImageNoDataError": "No table data or image to submit.", "successTitle": "Success", "singleImageExcelSuccessDesc": "Excel file generated and downloading.", "singleImageInvalidExcelApiError": "Invalid Excel API response.", "editCellTitle": "Edit Cell", "editCellTitleDynamic": "Edit Cell (Row {row}, Col {col})", "expandCell": "Expand to edit", "singleImageClickToUpload": "Click to upload", "singleImageDragAndDrop": "or drag and drop", "singleImageFormats": "PNG, JPG, etc.", "singleImageExtractButton": "Extract Table", "singleImageExtractingButton": "Extracting...", "generationSuccessTitle": "Files Ready for Download", "downloadFilesDescription": "Your generated files are listed below.", "downloadButton": "Download", "closeButton": "Close", "downloadLinkExpiration": "Note: Download links are valid for 30 minutes.", "viewDownloadLinks": "View Download Links", "stageButton": "Stage", "stagingData": "Staging...", "stagedRecordsTitle": "Staged Records", "openStagedRecords": "Open Staged Records", "stageSuccessTitle": "Staging Successful", "stageSuccessDescription": "Your data has been staged successfully, and the form has been cleared.", "stageErrorTitle": "Staging Failed", "stageError": "Failed to stage data.", "fetchStagedRecordsError": "Failed to fetch staged records.", "loadStagedRecordError": "Failed to load staged record.", "noStagedRecords": "No staged records found.", "loadingStagedRecords": "Loading staged records...", "recordName": "Record Name", "loadRecord": "Load", "loadRecordSuccess": "Staged record loaded successfully.", "deleteStagedRecordTitle": "Confirm Deletion", "deleteStagedRecordMessage": "Are you sure you want to permanently delete the staged record '{recordName}'? This action cannot be undone.", "confirmDelete": "Delete", "deleteButtonTooltip": "Delete Staged Record", "deleteRecordSuccess": "Record deleted successfully.", "stageDeleteError": "Failed to delete staged record.", "templateSelectPlaceholder": "Select a Template...", "fetchTemplateError": "Failed to fetch templates.", "qrRegisterTitle": "Register New Serial Number", "qrRegisterDescription": "Fill out the form to generate a new QR code.", "qrRegisterSubmitButton": "Generate QR Code", "registerAnother": "Register Another", "printButton": "Print", "qrRecordTitle": "Image Entry", "qrRecordDescription": "Upload an image with a QR code to fetch and update a record.", "qrUploadPrompt": "Click to upload an image", "qrUploadSubPrompt": "A QR code will be scanned automatically.", "qrImagePreviewAlt": "Uploaded image preview for QR scanning", "qrScanning": "Scanning for QR Code...", "qrScanSuccess": "QR Code scanned successfully!", "qrScanFailedTitle": "Scan Failed", "qrScanFailedDescription": "No QR code could be detected in the image. Please try a different image.", "qrRecordNotFound": "Record not found", "qrRecordDetailsTitle": "Record Details", "qrRecordCompleteTitle": "Record Complete", "qrRecordCompleteDescription": "All required images for this record have been uploaded.", "qrUploadGetUrlError": "Failed to get a secure upload link.", "qrUploadToS3Error": "Failed to upload image to S3", "qrUploadSuccessTitle": "Upload Successful", "qrUploadSuccessDescription": "The image has been successfully linked to this SN", "qrPreviewButton": "Preview", "qrPreviewTitle": "Excel Preview", "qrPreviewDescription": "This is a preview of the generated Excel file for SN: {sn}", "qrPreviewAlt": "Preview of generated Excel for {sn}", "qrBatchRegisterTitle": "Batch Register", "qrBatchRegisterDescription": "Upload an Excel file to register multiple records at once.", "qrBatchUploadPrompt": "Click to upload or drag & drop an Excel file here.", "qrBatchSubmitButton": "Upload & Generate", "qrBatchDownloadTemplate": "Download Template", "qrBatchInvalidFileType": "Invalid file type. Please upload an .xlsx or .xls file.", "qrBatchNoFileSelected": "Please select a file to upload.", "qrBatchUploading": "Uploading & Processing...", "qrBatchFileSelected": "File selected. Ready to upload.", "qrBatchCompleteTitle": "Batch Registration Complete", "qrBatchSuccessMessage": "{count} records were processed successfully.", "qrBatchDownloadZip": "Download ZIP Package", "qrSameItemWarning": "Submitting the same item repeatedly will generate the same serial number.", "clickToStartCapture": "Click here to start capture", "realTimeScanAndCapture": "Real-time QR scanning and photo capture", "stopCapture": "Stop Capture", "retakePhoto": "Retake", "nameplateDataNotFound": "Nameplate data not found", "saveSuccessTitle": "Save Successful", "nameplateDataUpdated": "Nameplate data has been updated", "saveFailedTitle": "Save Failed", "qrUploadScanTitle": "Upload or Scan QR Code", "qrUploadScanDescription": "Start shooting to scan a QR code, or upload an image from your device.", "capturedImageAlt": "Captured image", "startShooting": "Start Shooting", "alignQrCodeToEnable": "Align with QR code to enable capture", "cameraNotSupported": "Your browser does not support camera access.", "snLabel": "SN", "scanningInProgress": "Scanning... {progress}%", "recordDetailsTitle": "Record Details", "loadingRecord": "Loading record...", "errorLoadingRecord": "Error loading record", "templateLabel": "Template", "uploaderLabel": "Uploader", "uploadTimeLabel": "Upload Time", "uploadedImagesTitle": "Uploaded Images", "uploadedImageAlt": "Uploaded image", "noImagesUploaded": "No images have been uploaded for this record.", "previewButton": "Preview", "viewEditNameplateButton": "View/Edit Nameplate", "pleaseScanQrFirst": "Please scan or upload an image with a QR code first.", "imageViewerTitle": "Image Viewer", "nameplateEditorTitle": "Edit Nameplate: {sn}", "nameplateDataGridTitle": "Nameplate Content", "loadingNameplate": "Loading nameplate data...", "nameplateKeyHeader": "<PERSON><PERSON>", "nameplateValueHeader": "Value", "addRowButton": "Add Row", "cancelButton": "Cancel", "saveButton": "Save", "excelPreviewTitle": "Excel Preview", "adminQrRecordsTitle": "QR Record Management", "adminSearchQrRecordsPlaceholder": "Search by serial number, company, area, item, template, or username...", "adminLoadingQrRecords": "Loading QR records...", "adminNoQrRecordsFound": "No QR records found.", "adminSerialNumber": "Serial Number", "adminProgress": "Progress", "adminUpdatedAt": "Updated At", "adminViewDetails": "View Details", "adminRetakePhoto": "Retake Photo", "adminDeleteRecord": "Delete Record", "adminDeleteRecordTitle": "Delete QR Record", "adminDeleteRecordMessage": "Are you sure you want to permanently delete the record with serial number {serialNumber}? This action cannot be undone.", "adminDeleteRecordSuccess": "QR record deleted successfully.", "adminDeleteRecordError": "Failed to delete QR record.", "adminQrRecordDetails": "QR Record Details", "adminRecordImages": "Record Images", "adminRecordImage": "Record Image", "adminClose": "Close", "adminFetchingRecordDetailError": "Failed to fetch record details.", "adminQrRecordsDescription": "Manage QR code records, view details, and handle photo retakes.", "adminRecentRecordsDescription": "View and manage recent 30-day records from all users.", "adminNoImagesFound": "No Images Found", "adminNoImagesFoundMessage": "No images are available for this record.", "adminRetakePhotoSuccess": "Record Reset Successfully", "adminRetakePhotoSuccessMessage": "The record has been reset and is ready for new photos.", "adminRetakePhotoError": "Failed to reset record for retaking photos.", "adminAccountManagement": "Account Management", "adminRecordManagement": "Record Management", "adminRecordManagementOld": "Record Management (Old)", "adminFilterAll": "All Records", "adminFilterCompleted": "Completed", "adminFilterIncomplete": "Incomplete", "qrScannerInitError": "QR Scanner Initialization Failed", "clickToRetryInit": "Click to retry initialization", "retryAttempt": "Retry attempt", "initializingQrScanner": "Initializing QR Scanner...", "pleaseWait": "Please wait"}