'use client';

import { useState, useEffect, useCallback, createContext, useContext, ReactNode } from 'react';
import type { Language, Translations, TranslationKey } from '@/types';

// Import translations directly
import enTranslations from '@/locales/en.json';
import zhTranslations from '@/locales/zh.json';
import koTranslations from '@/locales/ko.json';

const translationsData: Record<Language, Translations> = {
  en: enTranslations as Translations,
  zh: zhTranslations as Translations,
  ko: koTranslations as Translations,
};

// Check the environment variable. Default to false if not set.
const isTranslationEnabled = process.env.NEXT_PUBLIC_TRANSLATION_ENABLED === 'true';
const defaultLanguage: Language = 'zh'; // Default to Chinese if translations are disabled
const enabledDefaultLanguage: Language = 'en'; // Default to English if enabled but no browser pref

interface TranslationContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: TranslationKey, params?: Record<string, string | number>) => string;
  isEnabled: boolean; // Expose the enabled status
}

const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

// Helper moved inside or made client-only if needed, but now logic is within Provider
const getClientSideInitialLanguage = (): Language => {
    if (isTranslationEnabled) {
        // This code now ONLY runs on the client
        if (typeof window !== 'undefined') {
            const storedLang = localStorage.getItem('language') as Language;
            if (storedLang && ['en', 'zh', 'ko'].includes(storedLang)) {
                return storedLang;
            }

            if (navigator.language) {
                const browserLang = navigator.language.split('-')[0] as Language;
                const supportedLangs: Language[] = ['en', 'zh', 'ko'];
                if (supportedLangs.includes(browserLang)) {
                    return browserLang;
                }
            }
        }
        return enabledDefaultLanguage; // Default to 'en' if enabled but no match
    }
    return defaultLanguage; // Default to 'zh' if disabled
};


export const TranslationProvider = ({ children }: { children: ReactNode }) => {
  // Initialize state with a placeholder or server default, useEffect corrects it client-side
  const [language, setLanguageState] = useState<Language>(
      isTranslationEnabled ? enabledDefaultLanguage : defaultLanguage
  );
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true); // Mark that we are on the client
    // Determine and set the correct initial language client-side
    const initialLang = getClientSideInitialLanguage();
    setLanguageState(initialLang);
    document.documentElement.lang = initialLang; // Set lang attribute on mount
  }, []); // Empty dependency array ensures this runs once on mount client-side

  const setLanguage = useCallback((lang: Language) => {
    if (isTranslationEnabled && isClient) { // Ensure it runs only client-side and if enabled
        setLanguageState(lang);
        localStorage.setItem('language', lang); // Persist language choice
        document.documentElement.lang = lang; // Update html lang attribute
    }
  }, [isClient]); // Depend on isClient

  const t = useCallback((key: TranslationKey, params?: Record<string, string | number>): string => {
    const currentLang = isTranslationEnabled ? language : defaultLanguage;
    let translation = (translationsData[currentLang] && translationsData[currentLang][key]) || key;

    if (params) {
        Object.keys(params).forEach(paramKey => {
            const regex = new RegExp(`{${paramKey}}`, 'g');
            translation = translation.replace(regex, String(params[paramKey]));
        });
    }

    return translation;
  }, [language]); // Depend on the current language state

  const value = { language, setLanguage, t, isEnabled: isTranslationEnabled };

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  );
};

export const useTranslations = (): TranslationContextType => {
  const context = useContext(TranslationContext);
  if (context === undefined) {
     // Provide a default implementation for server components or outside the provider
     return {
       language: defaultLanguage,
       setLanguage: () => {}, // No-op setter
       t: (key: TranslationKey, params?: Record<string, string | number>): string => {
           let translation = (translationsData[defaultLanguage] && translationsData[defaultLanguage][key]) || key;
            if (params) {
                Object.keys(params).forEach(paramKey => {
                    const regex = new RegExp(`{${paramKey}}`, 'g');
                    translation = translation.replace(regex, String(params[paramKey]));
                });
            }
           return translation;
       },
       isEnabled: isTranslationEnabled,
     };
    // Original error: throw new Error('useTranslations must be used within a TranslationProvider');
  }
  return context;
};
