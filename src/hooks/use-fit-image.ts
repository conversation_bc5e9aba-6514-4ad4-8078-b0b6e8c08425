
'use client';

import { useState, useLayoutEffect, RefObject } from 'react';

// This hook calculates the CSS transform required to fit a rotated element
// perfectly inside its container, similar to `object-fit: contain`.
export const useFitImage = (
  containerRef: RefObject<HTMLElement>,
  rotation: number // in degrees
) => {
  const [transform, setTransform] = useState('rotate(0deg) scale(1)');

  useLayoutEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    
    // We need to find the actual image element to get its native aspect ratio.
    // We assume the image is the first `img` element inside the container.
    const imageElement = container.querySelector('img');

    const calculateTransform = () => {
        const { width: containerWidth, height: containerHeight } = container.getBoundingClientRect();
        
        if (containerWidth === 0 || containerHeight === 0) return;

        // Use the image's natural dimensions for aspect ratio.
        // Fallback to container's aspect ratio if image isn't loaded yet.
        const imageAspectRatio = imageElement ? imageElement.naturalWidth / imageElement.naturalHeight : containerWidth / containerHeight;

        if (!imageAspectRatio || isNaN(imageAspectRatio)) return;

        // Convert rotation to radians for Math functions
        const angleRad = (Math.abs(rotation) % 180) * (Math.PI / 180);
        const sin = Math.sin(angleRad);
        const cos = Math.cos(angleRad);

        // Determine the initial 'contain' dimensions of the image before rotation
        let initialWidth, initialHeight;
        if (containerWidth / containerHeight > imageAspectRatio) {
            initialHeight = containerHeight;
            initialWidth = containerHeight * imageAspectRatio;
        } else {
            initialWidth = containerWidth;
            initialHeight = containerWidth / imageAspectRatio;
        }

        // Calculate the dimensions of the bounding box that would enclose the rotated image
        const boundingBoxWidth = initialWidth * cos + initialHeight * sin;
        const boundingBoxHeight = initialWidth * sin + initialHeight * cos;

        // Calculate the scale factor required to fit this bounding box inside the container
        const scale = Math.min(containerWidth / boundingBoxWidth, containerHeight / boundingBoxHeight);

        // Apply the rotation and the calculated scale
        setTransform(`rotate(${rotation}deg) scale(${scale})`);
    };

    // If the image is already loaded, calculate immediately.
    if (imageElement && imageElement.complete) {
        calculateTransform();
    } else if (imageElement) {
        // Otherwise, wait for the image to load to get its dimensions.
        imageElement.onload = calculateTransform;
    }

    // Recalculate on container resize.
    const resizeObserver = new ResizeObserver(calculateTransform);
    resizeObserver.observe(container);

    return () => {
        if (imageElement) {
            imageElement.onload = null;
        }
        resizeObserver.disconnect();
    };

  }, [containerRef, rotation]);

  return { transform };
};
