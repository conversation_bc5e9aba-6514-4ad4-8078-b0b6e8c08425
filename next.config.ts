
import type {NextConfig} from 'next';

const withPWA = require('next-pwa')({
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development'
});

const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    // domains: ['localhost'], // Add localhost if serving previews locally and facing issues
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'picsum.photos', // Keep picsum for potential placeholders
        port: '',
        pathname: '/**',
      },
      // Add other domains if you load images from external sources
      // {
      //   protocol: 'https',
      //   hostname: 'images.unsplash.com',
      // },
    ],
    // Allow blob URLs for local previews
    dangerouslyAllowSVG: true, // If using SVGs
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;", // Example CSP
    unoptimized: true, // Set to true if using blob URLs and facing issues, but consider performance implications
  },
   env: {
    // Make API base URL available client-side if needed, but prefer server-side fetches or API routes proxying
    // NEXT_PUBLIC_API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:3001/api',
  },
};

export default withPWA(nextConfig);
